<!DOCTYPE html>
<html lang="vi" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="UTF-8" />
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Alpine.js để toggle panel -->
  <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>D&C Fashions - Trang chủ</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-pink-50 text-gray-800 font-sans">

<!-- 🌸 Header -->
<header class="bg-white shadow sticky top-0 z-50">
  <div class="max-w-7xl mx-auto flex justify-between items-center px-4 py-4">
    <h1 class="text-2xl font-bold text-pink-600">D&C Fashions</h1>
    <nav class="space-x-6 hidden md:flex text-sm">
      <!-- Trong <header> -->
      <form th:action="@{/san-pham/danh-sach}" method="get" class="flex">
        <input
                type="text" name="q"
                th:value="${q}"
                placeholder="Tìm sản phẩm..."
                class="border rounded-l px-2 py-1 focus:outline-none"
        />
        <button
                type="submit"
                class="bg-pink-600 text-white px-3 rounded-r hover:bg-pink-700"
        >🔍</button>
      </form>

      <a href="#" class="hover:text-pink-600">Trang chủ</a>
      <a th:href="@{/san-pham/danh-sach}" class="hover:text-pink-600">Sản phẩm</a>
      <a href="#" class="hover:text-pink-600">Blog</a>
      <a href="#" class="hover:text-pink-600">Liên hệ</a>
    </nav>
    <div class="flex items-center gap-4">
      <a th:if="${khachHangLogin == null}"
         th:href="@{/khach-hang/dang-nhap}"
         class="text-sm bg-pink-500 text-white px-3 py-1.5 rounded hover:bg-pink-600">
        Đăng nhập
      </a>

      <div th:if="${khachHangLogin != null}" class="relative" x-data="{ open: false }">
        <img
                src='/uploads/avatar_nguoidung.jpg'
                alt="Avatar"
                class="w-10 h-10 rounded-full border-2 border-pink-500 cursor-pointer"
                @click="open = !open"
        />

        <div
                x-show="open"
                @click.outside="open = false"
                x-transition
                class="absolute right-0 mt-2 w-48 bg-white border rounded shadow-lg z-50"
        >
          <a th:href="@{/khach-hang/thong-tin}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-pink-100">
            👤 Thông tin tài khoản
          </a>
          <a th:href="@{/khach-hang/don-hang/{id}(id = ${ idKhachHang })}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-pink-100">
            > Lịch sử mua hàng
          </a>
          <form th:action="@{/khach-hang/dang-xuat}" method="post">
            <button type="submit" class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-100">
              🔓 Đăng xuất
            </button>
          </form>
        </div>
      </div>


      <a th:href="@{/gio-hang/hien_thi}" class="relative text-2xl text-pink-600">
        🛒
        <span th:if="${soLuongTrongGio > 0}" th:text="${soLuongTrongGio}"
              class="absolute -top-2 -right-2 text-xs bg-pink-500 text-white rounded-full px-1.5 py-0.5">0</span>
      </a>
    </div>
  </div>
</header>

<!-- 🐶 Hero Banner -->
<section class="bg-pink-100 py-16 text-center">
  <div class="max-w-3xl mx-auto">
    <h2 class="text-4xl font-extrabold mb-4">Thời trang thú cưng siêu đáng yêu 🐶🐱</h2>
    <p class="text-lg mb-6 text-gray-700">Khám phá bộ sưu tập quần áo & phụ kiện siêu xinh cho chó mèo.</p>
    <a th:href="@{/san-pham/danh-sach}" class="bg-pink-600 text-white px-6 py-3 rounded-full hover:bg-pink-700 transition">
      Mua sắm ngay
    </a>
  </div>
</section>
<!-- Đặt lên trên phần hiển thị sản phẩm -->
<div x-data="{open:false}" class="relative">
  <!-- Nút BỘ LỌC -->
  <button
          @click="open = !open"
          class="flex items-center gap-2 px-4 py-2 border rounded-md bg-white hover:bg-gray-100"
  >
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none"
         viewBox="0 0 24 24" stroke="currentColor">Bộ Lọc
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-.293.707L14
               14.414V19a1 1 0 01-1.447.894l-4-2A1 1 0 018 17v-2.586L3.293
               6.707A1 1 0 013 6V4z"/>
    </svg>

    <!-- icon -->
    BỘ LỌC
  </button>

  <!-- Panel lọc -->
  <div
          x-show="open"
          @click.outside="open = false"
          x-transition
          class="absolute z-10 mt-2 w-full max-w-md bg-white border rounded-md shadow-lg p-4"
  >
    <!-- ==== Lọc theo Loại thú ==== -->
    <div class="mb-4">
      <h3 class="font-semibold mb-2">Loại thú</h3>
      <div class="flex flex-wrap gap-2">
        <a
                th:each="lthu : ${loaiThus}"
                th:href="@{/san-pham/danh-sach(q=${q},danhMucId=${danhMucId},priceRange=${priceRange},loaiThuId=${lthu.id},chatLieuId=${chatLieuId},kieuDangId=${kieuDangId},xuatXuId=${xuatXuId})}"
                th:text="${lthu.ten}"
                th:classappend="${loaiThuId == lthu.id ? 'bg-pink-600 text-white' : 'bg-gray-100 text-gray-700'}"
                class="px-3 py-1 rounded-full border cursor-pointer transition"
        ></a>
      </div>
    </div>


    <!-- Danh mục -->
    <div class="mb-4">
      <h3 class="font-semibold mb-2">Danh mục</h3>
      <div class="flex flex-wrap gap-2">
        <a
                th:each="dm : ${danhMucs}"
                th:href="@{/san-pham/danh-sach(q=${q},danhMucId=${dm.id},priceRange=${priceRange},loaiThuId=${loaiThuId},chatLieuId=${chatLieuId},kieuDangId=${kieuDangId},xuatXuId=${xuatXuId})}"
                th:text="${dm.ten}"
                th:classappend="${danhMucId == dm.id ? 'bg-pink-600 text-white' : 'bg-gray-100 text-gray-700'}"
                class="px-3 py-1 rounded-full border cursor-pointer transition"
        ></a>
      </div>
    </div>
    <!-- Chất liệu -->
    <div class="mb-4">
      <h3 class="font-semibold mb-2">Chất liệu</h3>
      <div class="flex flex-wrap gap-2">
        <a
                th:each="cl : ${chatLieus}"
                th:href="@{/san-pham/danh-sach(q=${q},danhMucId=${danhMucId},priceRange=${priceRange},loaiThuId=${loaiThuId},chatLieuId=${cl.id},kieuDangId=${kieuDangId},xuatXuId=${xuatXuId})}"
                th:text="${cl.ten}"
                th:classappend="${chatLieuId == cl.id ? 'bg-pink-600 text-white' : 'bg-gray-100 text-gray-700'}"
                class="px-3 py-1 rounded-full border cursor-pointer transition"
        ></a>
      </div>
    </div>
    <!-- Kiểu dáng -->
    <div class="mb-4">
      <h3 class="font-semibold mb-2">Kiểu dáng</h3>
      <div class="flex flex-wrap gap-2">
        <a
                th:each="kd : ${kieuDangs}"
                th:href="@{/san-pham/danh-sach(q=${q},danhMucId=${danhMucId},priceRange=${priceRange},loaiThuId=${loaiThuId},chatLieuId=${chatLieuId},kieuDangId=${kd.id},xuatXuId=${xuatXuId})}"
                th:text="${kd.ten}"
                th:classappend="${kieuDangId == kd.id ? 'bg-pink-600 text-white' : 'bg-gray-100 text-gray-700'}"
                class="px-3 py-1 rounded-full border cursor-pointer transition"
        ></a>
      </div>
    </div>
    <!-- Xuất xứ -->
    <div class="mb-4">
      <h3 class="font-semibold mb-2">Xuất xứ</h3>
      <div class="flex flex-wrap gap-2">
        <a
                th:each="xx : ${xuatXus}"
                th:href="@{/san-pham/danh-sach(q=${q},danhMucId=${danhMucId},priceRange=${priceRange},loaiThuId=${loaiThuId},chatLieuId=${chatLieuId},kieuDangId=${kieuDangId},xuatXuId=${xx.id})}"
                th:text="${xx.ten}"
                th:classappend="${danhMucId == xx.id ? 'bg-pink-600 text-white' : 'bg-gray-100 text-gray-700'}"
                class="px-3 py-1 rounded-full border cursor-pointer transition"
        ></a>
      </div>
    </div>

    <!-- Giá -->
    <div>
      <h3 class="font-semibold mb-2">Giá</h3>
      <div class="flex flex-wrap gap-2">
        <a
                th:each="opt : ${priceOptions}"
                th:href="@{/san-pham/danh-sach(q=${q},danhMucId=${danhMucId},priceRange=${opt.value},loaiThuId=${loaiThuId},chatLieuId=${chatLieuId},kieuDangId=${kieuDangId},xuatXuId=${xuatXuId})}"
                th:text="${opt.label}"
                th:classappend="${priceRange == opt.value ? 'bg-pink-600 text-white' : 'bg-gray-100 text-gray-700'}"
                class="px-3 py-1 rounded-full border cursor-pointer transition"
        ></a>
      </div>
    </div>

    <!-- Nút HỦY BỘ LỌC -->
    <div class="mt-4 text-right">
      <a
              href="#"
              th:href="@{/san-pham/danh-sach}"
              @click="open = false"
              class="inline-block px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition"
      >
        Hủy bộ lọc
      </a>
    </div>
  </div>



  <!--        &lt;!&ndash; 🎀 Danh mục nổi bật &ndash;&gt;-->
  <!--<section class="py-12 bg-white">-->
  <!--    <div class="max-w-7xl mx-auto px-4">-->
  <!--        <h3 class="text-2xl font-bold text-center mb-10 text-pink-600">Danh mục nổi bật</h3>-->
  <!--        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">-->
  <!--            &lt;!&ndash; ... (các mục danh mục) ... &ndash;&gt;-->
  <!--        </div>-->
  <!--    </div>-->
  <!--</section>-->

  <!-- 🌟 Sản phẩm mới -->
  <section class="py-12">
    <div class="max-w-7xl mx-auto px-4">
      <h2 class="text-3xl font-bold text-center text-pink-600 mb-10">Tất cả sản phẩm</h2>
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        <div th:each="sp : ${danhSachSanPham}"
             class="product-card relative bg-white rounded-lg shadow hover:shadow-lg p-4 flex flex-col">

          <div class="relative">
            <img th:src="${'/uploads/' + sp.hinhAnhs[0]?.url}" alt="Ảnh sản phẩm"
                 class="w-full h-56 object-cover rounded-md mb-4"/>
            <div th:if="${phanTramGiamMap[sp.id] != null and phanTramGiamMap[sp.id] > 0}"
                 class="absolute top-2 left-2 bg-red-600 text-white text-xs font-bold px-2 py-1 rounded-full">
              <span th:text="'-' + ${phanTramGiamMap[sp.id]} + '%'">-10%</span>
            </div>
          </div>

          <h3 class="text-lg font-semibold text-pink-600 mb-1" th:text="${sp.ten}">Tên sản phẩm</h3>
          <p class="text-sm text-gray-600 flex-grow" th:text="${sp.moTa}">Mô tả sản phẩm</p>

          <p class="text-sm text-pink-700 font-semibold mt-2">
            <!-- Khi có giá bán và giá gốc, và giá bán thấp hơn giá gốc -->
            <span th:if="${giaBanMinMap[sp.id] != null and giaGocMinMap[sp.id] != null and giaBanMinMap[sp.id].compareTo(giaGocMinMap[sp.id]) < 0}">
    <span class="line-through text-gray-500 mr-2"
          th:text="${#numbers.formatDecimal(giaGocMinMap[sp.id], 0, 'COMMA', 0, 'POINT') + ' đ'}">
      100,000 đ
    </span>
    <span th:text="${#numbers.formatDecimal(giaBanMinMap[sp.id], 0, 'COMMA', 0, 'POINT') + ' đ'}">
      80,000 đ
    </span>
  </span>

            <!-- Ngược lại (không có giá bán thấp hơn), hiển thị khoảng giá gốc -->
            <span th:if="${giaGocMinMap[sp.id] != null and (giaBanMinMap[sp.id] == null or giaBanMinMap[sp.id].compareTo(giaGocMinMap[sp.id]) >= 0)}"
                  th:text="${#numbers.formatDecimal(giaGocMinMap[sp.id], 0, 'COMMA', 0, 'POINT') + ' đ – ' + #numbers.formatDecimal(giaGocMaxMap[sp.id], 0, 'COMMA', 0, 'POINT') + ' đ'}">
    100,000 đ – 150,000 đ
  </span>
          </p>


          <a th:href="@{/san-pham/chi-tiet/{id}(id=${sp.id})}"
             class="mt-4 inline-block text-center bg-pink-600 text-white py-2 px-4 rounded-full hover:bg-pink-700 transition">
            Xem chi tiết
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- 🐾 Footer -->
  <footer class="bg-white border-t mt-12">
    <div class="max-w-7xl mx-auto px-4 py-6 text-center text-sm text-gray-600">
      © 2025 Pet Closet. Yêu thương thú cưng mỗi ngày 🐾
    </div>
  </footer>

</body>
</html>