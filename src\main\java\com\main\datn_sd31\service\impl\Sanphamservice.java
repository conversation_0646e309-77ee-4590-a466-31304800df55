package com.main.datn_sd31.service.impl;

import com.main.datn_sd31.dto.san_pham_DTO.Sanphamform;
import com.main.datn_sd31.entity.SanPham;
import com.main.datn_sd31.repository.ChatLieuRepository;
import com.main.datn_sd31.repository.Chitietsanphamrepository;
import com.main.datn_sd31.repository.Danhmucrepository;
import com.main.datn_sd31.repository.Hinhanhrepository;
import com.main.datn_sd31.repository.Kieudangrepository;
import com.main.datn_sd31.repository.Loaithurepository;
import com.main.datn_sd31.repository.NhanVienRepository;
import com.main.datn_sd31.repository.SanPhamRepository;
import com.main.datn_sd31.repository.Thuonghieurepository;
import com.main.datn_sd31.repository.Xuatxurepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Collectors;

@Service
public class Sanphamservice {

    @Autowired private SanPhamRepository sanPhamRepo;
    @Autowired private ChatLieuRepository chatLieuRepo;
    @Autowired private Xuatxurepository xuatXuRepo;
    @Autowired private Danhmucrepository danhmucrepository;
    @Autowired private Kieudangrepository kieudangrepository;
    @Autowired private Thuonghieurepository thuonghieurepository;
    @Autowired private NhanVienRepository nhanvienrepository;
    @Autowired private Hinhanhrepository hinhanhrepository;
    @Autowired private Chitietsanphamrepository chitietsanphamrepository;
    @Autowired private Loaithurepository loaithurepository;

    public List<SanPham> getAll() {
        return sanPhamRepo.findAll();
    }

    public List<SanPham> search(
            String q,
            Integer danhMucId,
            Integer loaiThuId,
            Integer chatLieuId,
            Integer kieuDangId,
            Integer xuatXuId,
            String priceRange
    ) {
        // normalize keyword
        String keyword = (q == null ? "" : q.trim());

        // parse priceRange
        BigDecimal min = null, max = null;
        if (priceRange != null && !priceRange.isEmpty()) {
            String[] parts = priceRange.split("-");
            min = new BigDecimal(parts[0]);
            if (parts.length > 1) {
                max = new BigDecimal(parts[1]);
            }
        }

        // if no filter at all, trả về all
        if (keyword.isEmpty() && danhMucId == null && loaiThuId == null && chatLieuId == null && kieuDangId == null && xuatXuId == null &&  min == null) {
            return sanPhamRepo.findAll();
        }

        return sanPhamRepo.filter(keyword, danhMucId, loaiThuId, chatLieuId, kieuDangId, xuatXuId, min, max);
    }

    public SanPham createSanPham(Sanphamform form) {
        SanPham sp = new SanPham();
        sp.setMa(form.getMa());
        sp.setTen(form.getTen());
        sp.setMoTa(form.getMota());
        sp.setNgayTao(LocalDateTime.now());
        sp.setNgaySua(LocalDateTime.now());

        // Chuyển Integer → Boolean (0 = false, 1 = true)
        sp.setTrangThai(form.getTrangthai() != null && form.getTrangthai() == 1);

        sp.setChatLieu(chatLieuRepo.findById(form.getChatLieuId()).orElse(null));
        sp.setXuatXu(xuatXuRepo.findById(form.getXuatXuId()).orElse(null));
        sp.setDanhMuc(danhmucrepository.findById(form.getDanhMucId()).orElse(null));
        sp.setKieuDang(kieudangrepository.findById(form.getKieuDangId()).orElse(null));
        sp.setThuongHieu(thuonghieurepository.findById(form.getThuongHieuId()).orElse(null));
        sp.setLoaiThu(loaithurepository.findById(form.getLoaiThuId()).orElse(null));

         return sanPhamRepo.save(sp);
    }

    public SanPham findbyid(Integer id) {
        return sanPhamRepo.findById(id).orElse(null);
    }

    public void delete(Integer id) {
        hinhanhrepository.findBydeleteid(id);
        chitietsanphamrepository.findBydeleteid(id);
        sanPhamRepo.deleteById(id);
    }

    public List<SanPham> searchAdvanced(
            String q,
            Integer danhMucId,
            Integer loaiThuId,
            Integer sizeId,
            Integer mauSacId,
            Integer kieuDangId,
            Integer thuongHieuId,
            Integer xuatXuId,
            Integer priceRange
    ) {
        // normalize keyword
        String keyword = (q == null ? "" : q.trim());

        // parse priceRange
        BigDecimal min = null, max = null;
        if (priceRange != null) {
            min = BigDecimal.ZERO;
            max = new BigDecimal(priceRange);
        }

        // if no filter at all, trả về all
        if (keyword.isEmpty() && danhMucId == null && loaiThuId == null && 
            sizeId == null && mauSacId == null && kieuDangId == null && 
            thuongHieuId == null && xuatXuId == null && min == null) {
            return sanPhamRepo.findAll();
        }

        // Sử dụng method filter cơ bản trước
        List<SanPham> basicFiltered = sanPhamRepo.filter(
            keyword, danhMucId, loaiThuId, null, kieuDangId, xuatXuId, min, max
        );

        // Lọc thêm theo thương hiệu nếu có
        if (thuongHieuId != null) {
            basicFiltered = basicFiltered.stream()
                .filter(sp -> sp.getThuongHieu() != null && sp.getThuongHieu().getId().equals(thuongHieuId))
                .collect(Collectors.toList());
        }

        // Lọc theo size và màu sắc nếu có
        if (sizeId != null || mauSacId != null) {
            basicFiltered = basicFiltered.stream()
                .filter(sp -> sp.getChiTietSanPhams().stream()
                    .anyMatch(ct -> (sizeId == null || ct.getSize().getId().equals(sizeId)) &&
                                   (mauSacId == null || ct.getMauSac().getId().equals(mauSacId))))
                .collect(Collectors.toList());
        }

        return basicFiltered;
    }
}
