/* Product Page Specific Styles */
.product-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

/* Filter Sidebar */
.filter-sidebar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    height: fit-content;
    position: sticky;
    top: 120px;
}

.filter-section {
    margin-bottom: 2rem;
    border-bottom: 1px solid rgba(222, 173, 111, 0.2);
    padding-bottom: 1.5rem;
}

.filter-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.filter-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--accent-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-title iconify-icon {
    font-size: 1.2rem;
}

/* Custom Select Styling */
.custom-select {
    position: relative;
    width: 100%;
}

.select-trigger {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(222, 173, 111, 0.3);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    width: 100%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.95rem;
    color: #495057;
}

.select-trigger:hover {
    border-color: var(--accent-color);
    background: rgba(255, 255, 255, 0.95);
}

.select-trigger.active {
    border-color: var(--accent-color);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 3px rgba(222, 173, 111, 0.1);
}

.select-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border: 2px solid rgba(222, 173, 111, 0.3);
    border-top: none;
    border-radius: 0 0 12px 12px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    opacity: 0;
    transform: translateY(-10px);
    pointer-events: none;
    transition: all 0.3s ease;
}

.select-options.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

.select-option {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid rgba(222, 173, 111, 0.1);
}

.select-option:last-child {
    border-bottom: none;
}

.select-option:hover {
    background: rgba(222, 173, 111, 0.1);
    color: var(--accent-color);
}

.select-option.selected {
    background: rgba(222, 173, 111, 0.15);
    color: var(--accent-color);
    font-weight: 500;
}

/* Price Range Slider */
.price-range-container {
    padding: 1rem 0;
}

.price-range-slider {
    width: 100%;
    height: 6px;
    background: rgba(222, 173, 111, 0.2);
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
    margin: 1rem 0;
}

.price-range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: var(--accent-color);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(222, 173, 111, 0.3);
    transition: all 0.2s ease;
}

.price-range-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(222, 173, 111, 0.4);
}

.price-range-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: var(--accent-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 8px rgba(222, 173, 111, 0.3);
}

.price-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

/* Active Filters */
.active-filters {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-top: 1rem;
}

.filter-tag {
    background: linear-gradient(135deg, var(--accent-color), #d4a574);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(222, 173, 111, 0.3);
}

.filter-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(222, 173, 111, 0.4);
}

.filter-tag .remove-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.7rem;
    transition: all 0.2s ease;
}

.filter-tag .remove-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Product Grid */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.product-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
}

.product-content {
    padding: 1.5rem;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.product-category {
    color: var(--accent-color);
    font-size: 0.85rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
}

.product-price {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.current-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--accent-color);
}

.original-price {
    font-size: 1rem;
    color: #6c757d;
    text-decoration: line-through;
}

.discount-badge {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 600;
}

.product-actions {
    display: flex;
    gap: 0.75rem;
}

.btn-add-cart {
    flex: 1;
    background: linear-gradient(135deg, var(--accent-color), #d4a574);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-add-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(222, 173, 111, 0.4);
    color: white;
}

.btn-wishlist {
    background: rgba(222, 173, 111, 0.1);
    border: 2px solid rgba(222, 173, 111, 0.3);
    color: var(--accent-color);
    width: 45px;
    height: 45px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.btn-wishlist:hover {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

/* Loading Animation */
.loading-spinner {
    display: none;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(222, 173, 111, 0.2);
    border-top: 4px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 991px) {
    .filter-sidebar {
        position: static;
        margin-bottom: 2rem;
    }
    
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
    }
    
    .product-content {
        padding: 1rem;
    }
    
    .filter-tags {
        gap: 0.5rem;
    }
    
    .filter-tag {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
} 