<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="vi">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON> tiết sản phẩm - <PERSON> Closet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .modal-body img {
            max-width: 100%;
            height: auto;
        }
    </style>
    <style>
        /*CSS Thong bao*/
        .custom-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 40px 12px 18px; /* hoặc: padding: 12px 18px; */
            border-radius: 6px;
            color: white;
            z-index: 9999;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
            min-width: 280px;
            animation: fadeIn 0.3s ease-in-out;
            overflow: hidden;
        }


        .custom-toast.success {
            background-color: #28a745;
        }

        .custom-toast.error {
            background-color: #dc3545;
        }

        .custom-toast .close-btn {
            position: absolute;
            top: 6px;
            right: 8px;
            background: transparent;
            border: none;
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            line-height: 1;
            cursor: pointer;
            padding: 0;
        }

        .custom-toast .progress-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 4px;
            background-color: rgba(255, 255, 255, 0.7);
            animation: progressBar 4s linear forwards;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes progressBar {
            from { width: 100%; }
            to { width: 0%; }
        }

        /*CSS Thong bao*/
    </style>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

</head>
<body class="bg-pink-50 text-gray-800 font-sans">

<!-- Success Toast -->
<div id="customSuccess" class="custom-toast success" style="display:none;">
    <i class="bi bi-check-circle-fill me-2"></i>
    <span id="successMsg"></span>
    <button class="close-btn" onclick="closeToast('customSuccess')">&times;</button>
    <div class="progress-bar"></div>
</div>

<!-- Error Toast -->
<div id="customError" class="custom-toast error" style="display:none;">
    <i class="bi bi-x-circle-fill me-2"></i>
    <span id="errorMsg"></span>
    <button class="close-btn" onclick="closeToast('customError')">&times;</button>
    <div class="progress-bar"></div>
</div>

<!-- Header -->
<header class="bg-white shadow sticky top-0 z-50">
    <div class="max-w-7xl mx-auto flex justify-between items-center px-4 py-4">
        <h1 class="text-2xl font-bold text-pink-600">🐾 Pet Closet</h1>
        <div class="flex items-center gap-4">
            <a th:href="@{/gio-hang/hien_thi}" class="relative text-2xl text-pink-600">
                🛒
                <span th:if="${soLuongTrongGio > 0}" th:text="${soLuongTrongGio}"
                      class="absolute -top-2 -right-2 bg-pink-500 text-white text-xs px-1.5 py-0.5 rounded-full">0</span>
            </a>
        </div>
    </div>
</header>

<!-- Nội dung chi tiết -->
<div class="max-w-7xl mx-auto px-4 py-10">
    <h2 class="text-3xl font-bold text-pink-600 mb-6" th:text="${sanPham.ten}">Tên sản phẩm</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-10">
        <!-- Ảnh -->
        <div>
            <img id="mainImage"
                 th:if="${hinhanh != null and !hinhanh.isEmpty()}"
                 th:src="@{${hinhanh[0].url}}"
                 class="rounded shadow w-full h-[400px] object-cover mb-4"
                 alt="Ảnh sản phẩm">

            <!-- Thumbnail -->
            <div class="flex flex-wrap gap-2" th:if="${hinhanh != null and !hinhanh.isEmpty()}">
                <img th:each="img : ${hinhanh}" th:src="${img.url}"
                     class="w-20 h-20 rounded object-cover border cursor-pointer hover:ring-2 ring-pink-400"
                     onclick="document.getElementById('mainImage').src = this.src">
            </div>
            <p th:if="${hinhanh == null or hinhanh.isEmpty()}">Không có hình ảnh.</p>
        </div>

        <!-- Thông tin sản phẩm -->
        <div>
            <form th:action="@{/gio-hang/them}" method="post" class="space-y-4">
                <input type="hidden" name="sanPhamId" th:value="${sanPham.id}" />
                <input type="hidden" name="chiTietId" id="chiTietId" />

                <!-- Size -->
                <div>
                    <label class="font-medium">Chọn kích thước (<span th:text="${sizeCount}">0</span>)</label>
                    <div class="flex flex-wrap gap-2 mt-2">
                        <div th:each="sz : ${dsSize}">
                            <input type="radio" class="hidden peer" name="sizeId" th:id="'size-' + ${sz.id}" th:value="${sz.id}" onchange="updateGia()" required />
                            <label th:for="'size-' + ${sz.id}" th:text="${sz.ten}"
                                   class="px-4 py-2 border rounded-full cursor-pointer peer-checked:bg-pink-600 peer-checked:text-white">
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Màu -->
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2">Chọn màu sắc:</label>

                    <div class="flex flex-wrap gap-3">
                        <div th:each="mau : ${dsMauSac}">
                            <input type="radio" class="hidden peer" name="mauSacId"
                                   th:id="'mau-' + ${mau.id}" th:value="${mau.id}"
                                   onchange="updateGia()" required />

                            <label th:for="'mau-' + ${mau.id}"
                                   class="w-9 h-9 rounded-full border-2 border-gray-300 peer-checked:ring-2 ring-pink-500 inline-block cursor-pointer transition"
                                   th:style="'background-color:' + ${mau.ten}"
                                   th:title="${mau.ten}">
                            </label>
                        </div>
                    </div>
                </div>


                <!-- Tồn kho -->
                <p class="text-sm text-green-600 font-medium" id="tonKhoText"></p>

                <!-- Số lượng -->
                <div>
                    <label class="block font-medium">Số lượng</label>
                    <input type="number" name="soLuong" value="1" min="1"
                           class="mt-1 w-24 border rounded px-3 py-1" required />
                </div>

                <!-- Giá -->
                <p id="giaText" class="text-lg font-semibold text-pink-600">Chọn màu và size để xem giá</p>
                <!-- Nút xem bảng size -->

                <button type="button"
                        onclick="document.getElementById('sizeModal').classList.remove('hidden')"
                        class="border border-blue-500 text-blue-600 px-4 py-2 rounded-full hover:bg-blue-50 transition flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                    Xem bảng size
                </button>


                <!-- Modal Tailwind -->
                <div id="sizeModal" class="fixed inset-0 bg-black/30 z-50 flex items-center justify-center hidden">
                    <div class="bg-white rounded-2xl shadow-lg w-full max-w-xl p-6 relative">

                        <!-- Nút đóng -->
                        <button type="button"
                                onclick="document.getElementById('sizeModal').classList.add('hidden')"
                                class="absolute top-3 right-3 text-gray-500 hover:text-red-500"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                                 stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>

                        <!-- Tiêu đề -->
                        <h2 class="text-lg font-semibold text-pink-600 mb-4 flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-pink-600" viewBox="0 0 24 24"
                                 fill="currentColor">
                                <path d="M4 6h16M4 12h16M4 18h16"/>
                            </svg>
                            Bảng size cho chó & mèo
                        </h2>

                        <!-- Bảng -->
                        <div class="overflow-x-auto">
                            <table class="w-full text-center text-sm border border-gray-200">
                                <thead class="bg-pink-100 text-pink-800">
                                <tr>
                                    <th class="py-2 px-3 border">Size</th>
                                    <th class="py-2 px-3 border">Cân nặng chó (kg)</th>
                                    <th class="py-2 px-3 border">Cân nặng mèo (kg)</th>
                                </tr>
                                </thead>
                                <tbody class="text-gray-700">
                                <tr class="hover:bg-gray-50">
                                    <td class="py-2 px-3 border font-medium">XS</td>
                                    <td class="py-2 px-3 border">2 - 4</td>
                                    <td class="py-2 px-3 border">1 - 3</td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="py-2 px-3 border font-medium">S</td>
                                    <td class="py-2 px-3 border">4 - 6</td>
                                    <td class="py-2 px-3 border">3 - 5</td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="py-2 px-3 border font-medium">M</td>
                                    <td class="py-2 px-3 border">6 - 9</td>
                                    <td class="py-2 px-3 border">5 - 7</td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="py-2 px-3 border font-medium">L</td>
                                    <td class="py-2 px-3 border">9 - 12</td>
                                    <td class="py-2 px-3 border">7 - 9</td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="py-2 px-3 border font-medium">XL</td>
                                    <td class="py-2 px-3 border">12 - 15</td>
                                    <td class="py-2 px-3 border">9 - 12</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Nút đóng ở dưới -->
                        <div class="text-center mt-4">
                            <button type="button"
                                    onclick="document.getElementById('sizeModal').classList.add('hidden')"
                                    class="px-4 py-2 text-sm rounded-full border border-gray-300 hover:bg-gray-100"
                            >
                                Đóng
                            </button>
                        </div>
                    </div>
                </div>



                <button type="submit" id="btnThemGioHang"
                        class="bg-pink-600 text-white px-6 py-2 rounded-full hover:bg-pink-700 transition">
                    Thêm vào giỏ hàng
                </button>
            </form>

            <a th:href="@{/san-pham/danh-sach}" class="inline-block mt-6 text-sm text-gray-600 hover:underline">
                ← Quay lại danh sách
            </a>
        </div>
    </div>


    <section class="mt-8">
        <h3 class="text-xl font-semibold mb-4">ĐÁNH GIÁ SẢN PHẨM</h3>

        <!-- Tổng hợp đánh giá -->
        <div class="border p-4 mb-6">
            <!-- Hiển thị điểm trung bình -->
            <div class="flex items-center gap-2">
                <span class="text-3xl font-bold" th:text="${avgRatingStr}">0,0</span>
                <span>/ 5 sao</span>
            </div>

            <!-- Hiển thị sao -->
            <div class="mt-2 flex items-center gap-1">
                <!-- full stars -->
                <span th:each="i : ${#numbers.sequence(1, fullStars)}"
                      class="text-yellow-500 text-xl">★</span>

                <!-- half star nếu có -->
                <span th:if="${halfStar}"
                      class="text-yellow-500 text-xl">⯪</span>

                <!-- empty stars -->
                <span th:each="i : ${#numbers.sequence(1, emptyStars)}"
                      class="text-gray-300 text-xl">★</span>
            </div>

            <div class="mt-2 space-x-2">
                <a th:href="@{/san-pham/chi-tiet/{id}(id=${sanPham.id})}"
                   th:classappend="${starFilter == null} ? 'bg-pink-600 text-white' : ''"
                   class="px-3 py-1 border rounded">Tất cả (<span th:text="${totalCount}">0</span>)</a>
                <a th:each="s : ${#numbers.sequence(5,1,-1)}"
                   th:href="@{/san-pham/chi-tiet/{id}(id=${sanPham.id},star=${s})}"
                   th:classappend="${starFilter == s} ? 'bg-pink-600 text-white' : ''"
                   class="px-3 py-1 border rounded"
                   th:text="${s} + ' sao '">5 sao </a>
            </div>

        </div>

        <!-- Danh sách review -->
        <div th:each="rv : ${reviews}"
             class="border-b py-4"
             th:with="
       cnt=${rv.soSao != null
         ? (rv.soSao > 5 ? 5 : rv.soSao)
         : 0},
       empties=${5 - cnt}
     ">
            <div class="flex items-center gap-2 mb-1">
                <span class="font-semibold" th:text="${rv.khachHang.ten}">Khách hàng</span>
                <div class="flex items-center gap-1">
                    <!-- full stars -->
                    <span th:each="i : ${#numbers.sequence(1,5)}"
                          th:text="${i <= (rv.soSao != null ? (rv.soSao > 5 ? 5 : rv.soSao) : 0)} ? '★' : '★'"
                          th:classappend="${i <= (rv.soSao != null ? (rv.soSao > 5 ? 5 : rv.soSao) : 0)}
                         ? 'text-yellow-500' : 'text-gray-300'"
                          class="text-xl">
                        ★
                      </span>
                </div>
            </div>

            <div class="text-sm text-gray-500 mb-2"
                 th:text="${#temporals.format(rv.thoiGian, 'dd/MM/yyyy HH:mm')}">
                01/01/2025 12:00
            </div>
            <p class="mb-2" th:text="${rv.noiDung}">Nội dung đánh giá...</p>
            <div th:if="${rv.hinhAnh}">
                <img th:src="${rv.hinhAnh}"
                     class="max-w-xs rounded"
                     alt="Hình ảnh đánh giá"/>
            </div>
        </div>

        <!-- pagination -->
        <div class="flex justify-center mt-6 space-x-2">
            <a th:if="${currentPage > 0}"
               th:href="@{/san-pham/chi-tiet/{id}(id=${sanPham.id},page=${currentPage-1},star=${starFilter})}"
               class="px-3 py-1 border rounded">« Trước</a>

            <a th:each="i : ${#numbers.sequence(0,totalPages-1)}"
               th:href="@{/san-pham/chi-tiet/{id}(id=${sanPham.id},page=${i},star=${starFilter})}"
               th:classappend="${i == currentPage} ? 'bg-pink-600 text-white' : ''"
               class="px-3 py-1 border rounded"
               th:text="${i+1}">1</a>

            <a th:if="${currentPage < totalPages-1}"
               th:href="@{/san-pham/chi-tiet/{id}(id=${sanPham.id},page=${currentPage+1},star=${starFilter})}"
               class="px-3 py-1 border rounded">Tiếp »</a>
        </div>

    </section>

</div>
<!-- Bootstrap JS (bao gồm cả Popper) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

<!-- JS xử lý giá -->
<script th:inline="javascript">
    const chiTietSanPhams = /*[[${dsChiTietSanPham}]]*/ [];

    function updateGia() {
        const sizeId = document.querySelector('input[name="sizeId"]:checked');
        const mauId = document.querySelector('input[name="mauSacId"]:checked');
        const giaText = document.getElementById('giaText');
        const tonKhoText = document.getElementById('tonKhoText');
        const hiddenInput = document.getElementById('chiTietId');
        const btnThem = document.getElementById('btnThemGioHang');

        if (!sizeId || !mauId) {
            giaText.textContent = "Chọn màu và size để xem giá";
            tonKhoText.textContent = "";
            hiddenInput.value = "";
            btnThem.disabled = true;
            return;
        }

        const size = parseInt(sizeId.value);
        const mau = parseInt(mauId.value);
        const matched = chiTietSanPhams.find(ct => ct.size.id === size && ct.mauSac.id === mau);

        if (matched && matched.soLuongTon > 0) {
            giaText.textContent = new Intl.NumberFormat('vi-VN', {
                style: 'currency', currency: 'VND'
            }).format(matched.giaBan);
            tonKhoText.textContent = "Tồn kho: " + matched.soLuongTon + " sản phẩm";
            hiddenInput.value = matched.id;
            btnThem.disabled = false;
        } else {
            giaText.textContent = "Sản phẩm đang hết hàng";
            tonKhoText.textContent = "";
            hiddenInput.value = "";
            btnThem.disabled = true;
        }
    }
</script>

<!--  Thong bao-->
<script th:inline="javascript">
    /*<![CDATA[*/
    const successMsg = [[${success} == null ? 'null' : '' + ${success} + '']];
    const errorMsg = [[${error} == null ? 'null' : '' + ${error} + '']];

    function showCustomToast(type, message) {
        const toastId = type === 'success' ? 'customSuccess' : 'customError';
        const toastEl = document.getElementById(toastId);
        const msgEl = document.getElementById(type === 'success' ? 'successMsg' : 'errorMsg');
        const progressBar = toastEl.querySelector('.progress-bar');

        msgEl.textContent = message;
        toastEl.style.display = 'block';

        // Khởi động lại animation nếu toast được hiển thị lại
        progressBar.style.animation = 'none';
        void progressBar.offsetWidth; // force reflow
        progressBar.style.animation = null;

        // Auto close sau 4s
        setTimeout(() => {
            toastEl.style.display = 'none';
        }, 4000);
    }

    function closeToast(toastId) {
        const toastEl = document.getElementById(toastId);
        toastEl.style.display = 'none';
    }

    document.addEventListener("DOMContentLoaded", function () {
        if (successMsg && successMsg !== "null") showCustomToast('success', successMsg);
        if (errorMsg && errorMsg !== "null") showCustomToast('error', errorMsg);
    });
    /*]]>*/
</script>
<!--  Thong bao-->

</body>
</html>