<!DOCTYPE html>
<!--Cấu hình layout cho Thymleaf-->
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{admin/layout}">
<head>
    <title>Home</title>
    <!--    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet"-->
    <!--          integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT" crossorigin="anonymous">-->

    <!--    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">-->
<!--    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css" />-->

    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css" />

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <style>
        .filter-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .filter-buttons .btn {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .filter-buttons a:hover {
            color: white;
        }

        .filter-buttons a {
            color: #0d6efd;
            text-decoration: none;
            transition: color 0.2s ease;
        }

        /*CSS Table*/
        /* Vùng chứa bảng: nền trắng, bo góc, đổ bóng nhẹ */
        .table-section {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
        }

        /* Header bảng: màu nền xám nhạt */
        .table-section .table thead th {
            background-color: #f1f1f1;
            font-weight: 600;
            text-align: center;
            vertical-align: middle;
            padding: 12px 8px;
        }

        /* Ô trong bảng: căn giữa, có padding và hạn chế tràn text */
        .table td, .table th {
            vertical-align: middle;       /* Căn giữa theo chiều dọc */
            text-align: center;           /* Căn giữa nội dung */
            padding: 12px 8px;            /* Giãn dòng cho đẹp */
            white-space: nowrap;          /* Không xuống dòng */
            overflow: hidden;             /* Ẩn nội dung vượt quá */
            max-width: 150px;             /* Giới hạn chiều rộng ô */
            text-overflow: ellipsis;      /* Hiển thị dấu ... nếu quá dài */
        }

        /* Hiệu ứng hover cho từng dòng bảng */
        .table tbody tr:hover {
            background-color: #f8f9fa;
            transition: background-color 0.2s ease;
        }

        /* Nút thao tác (icon) */
        .action-btn {
            border: none;
            background: none;
            cursor: pointer;
            font-size: 1.2rem;
            padding: 5px;
            margin: 0 2px;
            transition: color 0.2s ease;
        }

        /* Icon thao tác: Màu xanh lá khi dùng class 'accept' */
        .action-btn.accept {
            color: green;
        }

        /* Icon thao tác: Màu đỏ khi dùng class 'cancel' */
        .action-btn.cancel {
            color: red;
        }

        /* Nếu có thể: bổ sung class cho các icon khác nhau (in, xem chi tiết, v.v.) */
        .action-btn.info {
            color: #0d6efd;
        }
        /*CSS Table*/

        button.disabled-custom {
            opacity: 0.5;
            cursor: not-allowed !important;
            pointer-events: auto; /* Cho phép hover nhưng không click vì có 'disabled' */
        }

    </style>
</head>
<body>

<section layout:fragment="content">
    <div class="container mt-4">
        <h2 class="mb-4 text-center">Danh Sách Hóa Đơn</h2>

        <!-- Filter Section -->
        <div class="filter-section">
            <form method="get" th:action="@{/admin/hoa-don}">
                <div class="row g-3 align-items-end mb-4">
                    <div class="col-md-3">
                        <label for="startDate" class="form-label">Ngày bắt đầu</label>
                        <input type="date" class="form-control" id="startDate" name="startDate"
                               th:value="${startDate}">
                    </div>
                    <div class="col-md-3">
                        <label for="endDate" class="form-label">Ngày kết thúc</label>
                        <input type="date" class="form-control" id="endDate" name="endDate"
                               th:value="${endDate}">
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary w-100">Tìm kiếm</button>
                    </div>
                    <div class="col-md-3">
                        <a th:href="@{/admin/hoa-don}" class="btn btn-outline-secondary w-100">Làm mới</a>
                    </div>
                </div>
            </form>
            <div class="row mb-3 align-items-center">
                <div class="col-md-2">
                    <label for="entries" class="form-label">Hiển thị</label>
                    <select id="entries" class="form-select" onchange="changePageSize(this.value)">
                        <option value="10" th:selected="${pageInfo.size == 10}">10</option>
                        <option value="25" th:selected="${pageInfo.size == 25}">25</option>
                        <option value="50" th:selected="${pageInfo.size == 50}">50</option>
                        <option value="100" th:selected="${pageInfo.size == 100}">100</option>
                    </select>
                </div>
                <div class="col-md-10 text-end">
                    <form th:action="@{/admin/hoa-don/search}" method="get">
                        <div>
                            <label for="keyword" class="form-label me-2">Tìm kiếm:</label>
                            <input type="text" name="keyword" id="keyword" class="form-control d-inline-block"
                                   style="width: 400px;"
                                   placeholder="Tìm theo mã, tên KH, SĐT">
                            <button class="btn btn-primary">Tìm</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Table Section -->
        <div class="table-section" style="width: initial;">
            <div class="filter-buttons mb-3">
                <button class="btn btn-outline-primary">
                    <a th:href="@{/admin/hoa-don}">Tất cả</a>
                </button>
                <button th:each="status : ${T(com.main.datn_sd31.Enum.TrangThaiLichSuHoaDon).values()}"
                        th:if="${(status.value == 5 or status.value == 8 or status.value == 9 or status.value == 10)}"
                        class="btn btn-outline-primary position-relative">
                    <a th:href="@{/admin/hoa-don(trang-thai=${status.name()})}" th:text="${status.moTa}"></a>
<!--                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"-->
<!--                          th:if="${trangThaiCount[status.moTa] != null and-->
<!--                                   trangThaiCount[status.moTa] != 0 and-->
<!--                                   !(status.name() == 'HOAN_THANH' or status.name() == 'DA_HOAN' or status.name() == 'HUY')}"-->
<!--                          th:text="${trangThaiCount[status.moTa]}">-->
<!--                    </span>-->
                </button>
            </div>

            <table id="billTable" class="table table-bordered table-hover">
                <thead>
                <tr>
                    <th>STT</th>
                    <th>Mã</th>
                    <th>Khách hàng</th>
                    <th>Trạng Thái</th>
                    <th>Thành Tiền</th>
                    <th>Thanh toán</th>
                    <th>Loại hóa đơn</th>
                    <th>Ngày tạo</th>
                    <th>Cập nhật lần cuối</th>
                    <th>Thao Tác</th>
                </tr>
                </thead>
                <tbody>
                <tr th:each="hoaDon, index : ${hoaDonList}">
                    <td th:text="${index.count}"></td>
                    <td th:text="${hoaDon.ma}"></td>
                    <td th:text="${hoaDon.tenKH != null ? hoaDon.tenKH : 'Khách lẻ' }"></td>
                    <td th:text="${hoaDon.trangThaiLichSuHoaDonMoTa}"></td>
                    <td th:text="${@utils.formatCurrency(hoaDon.thanhTien) + ' đ'}"></td>
                    <td th:text="${hoaDon.trangThaiHoaDonString}"></td>
                    <td th:text="${hoaDon.loaihoadon}"></td>
                    <td th:text="${hoaDon.ngayTao}"></td>
                    <td th:text="${hoaDon.capNhatLanCuoi}"></td>
                    <td>
                        <!-- Nút mở Modal -->
                        <button class="action-btn accept" data-bs-toggle="modal"
                                th:attr="data-bs-target='#modalCapNhatTrangThai__' + ${hoaDon.ma}"
                                th:classappend="${@hoa_don_utils.khongChoPhepCapNhatTrangThai(hoaDon.trangThaiLichSuHoaDon) ? '' : 'disabled-custom'}"
                                th:disabled="${!@hoa_don_utils.khongChoPhepCapNhatTrangThai(hoaDon.trangThaiLichSuHoaDon)}"
                                th:title="${!@hoa_don_utils.khongChoPhepCapNhatTrangThai(hoaDon.trangThaiLichSuHoaDon) ? 'Không thể cập nhật trạng thái hiện tại' : ''}"
                        >
                            <i class="fa-solid fa-retweet"></i>
                        </button>
                        <!-- In-->
<!--                        <button type="button"-->
<!--                                class="action-btn cancel"-->
<!--                                data-ma-hoa-don="${hoaDon.ma}"-->
<!--                                onclick="printInvoice(this)"-->
<!--                                th:classappend="${@hoa_don_utils.choPhepInHoaDon(hoaDon.trangThaiLichSuHoaDon, hoaDon.trangThaiHoaDonInteger) ? '' : 'disabled-custom'}"-->
<!--                                th:disabled="${!@hoa_don_utils.choPhepInHoaDon(hoaDon.trangThaiLichSuHoaDon, hoaDon.trangThaiHoaDonInteger)}"-->
<!--                                th:title="${!@hoa_don_utils.choPhepInHoaDon(hoaDon.trangThaiLichSuHoaDon, hoaDon.trangThaiHoaDonInteger) ? 'Không thể in ở trạng thái hiện tại' : ''}">-->
<!--                            <i class="fa-solid fa-print"></i>-->
<!--                        </button>-->
                        <a th:href="@{'/admin/hoa-don/detail/' + ${hoaDon.ma} + '/pdf'}"
                           class="action-btn cancel"
                           th:classappend="${@hoa_don_utils.choPhepInHoaDon(hoaDon.trangThaiLichSuHoaDon, hoaDon.trangThaiHoaDonInteger) ? '' : 'disabled-custom'}"
                           th:attr="title=${!@hoa_don_utils.choPhepInHoaDon(hoaDon.trangThaiLichSuHoaDon, hoaDon.trangThaiHoaDonInteger) ? 'Không thể in ở trạng thái hiện tại' : null}"
                           th:if="${@hoa_don_utils.choPhepInHoaDon(hoaDon.trangThaiLichSuHoaDon, hoaDon.trangThaiHoaDonInteger)}">
                            <i class="fa-solid fa-print"></i>
                        </a>
                        <!-- Detail-->
                        <button class="action-btn accept">
                            <a th:href="@{/admin/hoa-don/detail(maHoaDon=${hoaDon.ma})}">
                                <i class="fa-solid fa-circle-info"></i>
                            </a>
                        </button>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>

<!--        &lt;!&ndash; Modal xác nhận in hóa đơn&ndash;&gt;-->
<!--        <div th:each="hoaDon : ${hoaDonList}">-->
<!--            <div class="modal fade"-->
<!--                 th:id="${'confirmPrintModal-' + hoaDon.ma}"-->
<!--                 tabindex="-1"-->
<!--                 aria-labelledby="confirmPrintModalLabel"-->
<!--                 aria-hidden="true">-->
<!--                <div class="modal-dialog modal-dialog-centered">-->
<!--                    <div class="modal-content">-->
<!--                        <div class="modal-header">-->
<!--                            <h5 class="modal-title" id="confirmPrintModalLabel">Xác nhận in hóa đơn</h5>-->
<!--                            <button type="button" class="btn-close" data-bs-dismiss="modal"-->
<!--                                    aria-label="Close"></button>-->
<!--                        </div>-->
<!--                        <div class="modal-body">-->
<!--                            Bạn có chắc chắn muốn in hóa đơn <strong th:text="${hoaDon.ma}"></strong> không?-->
<!--                        </div>-->
<!--                        <div class="modal-footer">-->
<!--                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>-->
<!--                            <button type="button"-->
<!--                                    class="btn btn-primary"-->
<!--                                    th:attr="data-ma-hoa-don=${hoaDon.ma}"-->
<!--                                    onclick="confirmPrint(this)">-->
<!--                                In-->
<!--                            </button>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->

        <!-- Modal riêng cho mỗi hóa đơn -->
        <div th:each="hoaDon : ${hoaDonList}">
            <div class="modal fade" th:id="'modalCapNhatTrangThai__' + ${hoaDon.ma}" tabindex="-1"
                 aria-labelledby="modalLabel"
                 aria-hidden="true">
                <div class="modal-dialog">
                    <form th:action="@{/admin/hoa-don/cap-nhat-trang-thai}" method="post">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="modalLabel">Cập nhật trạng thái đơn hàng</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Đóng"></button>
                            </div>
                            <div class="modal-body">
                                <input type="hidden" name="maHoaDon" th:value="${hoaDon.ma}"/>

                                <div class="mb-3" th:if="${hoaDon.trangThaiLichSuHoaDon.name() != 'HOAN_THANH'
                                                                and hoaDon.trangThaiLichSuHoaDon.name() != 'DA_HOAN'
                                                                and hoaDon.trangThaiLichSuHoaDon.name() != 'HUY'}">
                                    <label class="form-label">Chọn trạng thái mới:</label>
                                    <select name="trangThaiMoi" class="form-select">
                                        <option th:each="tt : ${trangThaiHopLeMap[hoaDon.ma]}"
                                                th:value="${tt.value}"
                                                th:text="${tt.moTa}">
                                        </option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Ghi chú</label>
                                    <textarea class="form-control" name="ghiChu" rows="3"
                                              placeholder="Nhập ghi chú..."></textarea>
                                </div>

<!--                                <div class="form-check">-->
<!--                                    <input class="form-check-input" type="checkbox" name="quayLui" value="true"-->
<!--                                           th:id="'quayLui_' + ${hoaDon.ma}">-->
<!--                                    <label class="form-check-label" th:for="'quayLui_' + ${hoaDon.ma}">-->
<!--                                        Quay lại trạng thái trước đó-->
<!--                                    </label>-->
<!--                                </div>-->
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng
                                </button>
                                <button type="submit" class="btn btn-primary">Xác nhận</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <div class="row mt-3">
            <div class="col-md-6">
                <p th:if="${pageInfo.totalElements} > 0"
                   th:text="'Đang hiển thị từ ' + ${pageInfo.number * pageInfo.size + 1} + ' đến ' + ${pageInfo.number * pageInfo.size + pageInfo.numberOfElements} + ' của ' + ${pageInfo.totalElements} + ' bản ghi'"></p>
                <p th:if="${pageInfo.totalElements} == 0">Không có dữ liệu</p>
            </div>
            <div class="col-md-6 text-end">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-end mb-0">
                        <li class="page-item" th:classappend="${pageInfo.first} ? 'disabled'">
                            <a class="page-link"
                               th:href="@{/admin/hoa-don(page=${pageInfo.number - 1}, trangThai=${param.trangThai}, size=${pageInfo.size})}">Trước</a>
                        </li>
                        <li th:if="${pageInfo.totalPages > 0}"
                            class="page-item"
                            th:each="i : ${#numbers.sequence(0, pageInfo.totalPages - 1)}"
                            th:classappend="${i == pageInfo.number} ? 'active'">

                            <a class="page-link"
                               th:href="@{/admin/hoa-don(page=${i}, trangThai=${param.trangThai}, size=${pageInfo.size})}"
                               th:text="${i + 1}"></a>
                        </li>
                        <li class="page-item" th:classappend="${pageInfo.last} ? 'disabled'">
                            <a class="page-link"
                               th:href="@{/admin/hoa-don(page=${pageInfo.number + 1}, trangThai=${param.trangThai}, size=${pageInfo.size})}">Sau</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!--In-->
    <script>
        function printInvoice(btn) {
            const ma = btn.getAttribute("data-ma-hoa-don");
            window.location.href = "/admin/hoa-don/detail/" + ma + "/pdf";
        }
    </script>

    <!--Change size Pagination-->
    <script>
        function changePageSize(size) {
            const url = new URL(window.location.href);
            url.searchParams.set('size', size);
            url.searchParams.set('page', 0); // reset về page 0 khi đổi size
            window.location.href = url.toString();
        }
    </script>

</section>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-j1CDi7MgGQ12Z7Qab0qlWQ/Qqz24Gc6BM0thvEMVjHnfYGF0rmFCozFSxQBxwHKO"
        crossorigin="anonymous"></script>


<!--&lt;!&ndash;Đóng modal In&ndash;&gt;-->
<!--<script>-->
<!--    function confirmPrint(buttonEl) {-->
<!--        const maHoaDon = buttonEl.getAttribute('data-ma-hoa-don');-->
<!--        const modalId = 'confirmPrintModal-' + maHoaDon;-->
<!--        const modalEl = document.getElementById(modalId);-->
<!--        const modal = bootstrap.Modal.getOrCreateInstance(modalEl);-->
<!--        modal.hide();-->

<!--        setTimeout(() => {-->
<!--            window.location.href = `/admin/hoa-don/detail1/${maHoaDon}/pdf`;-->
<!--        }, 300);-->
<!--    }-->
<!--</script>-->



</body>
</html>