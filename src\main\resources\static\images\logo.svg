<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 22.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 125.9 16.1" style="enable-background:new 0 0 125.9 16.1;" xml:space="preserve">
<style type="text/css">
	.st0{fill:url(#SVGID_1_);}
	.st1{fill:url(#SVGID_2_);}
	.st2{fill:url(#SVGID_3_);}
	.st3{fill:url(#SVGID_4_);}
	.st4{fill:url(#SVGID_5_);}
	.st5{fill:url(#SVGID_6_);}
	.st6{fill:url(#SVGID_7_);}
	.st7{fill:url(#SVGID_8_);}
	.st8{fill:url(#SVGID_9_);}
	.st9{fill:url(#SVGID_10_);}
	.st10{fill:#FFFFFF;stroke:#000000;stroke-miterlimit:10;}
</style>
<g>
	
		<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="-8.125" y1="1311.05" x2="111.7512" y2="1311.05" gradientTransform="matrix(1 0 0 1 0 -1303)">
		<stop  offset="0" style="stop-color:#115BC4"/>
		<stop  offset="1" style="stop-color:#05BDFD"/>
	</linearGradient>
	<path class="st0" d="M15.2,5.5l-4.5-0.7l-2-4.3C8.6,0.2,8.3,0,7.9,0C7.6,0,7.3,0.2,7.1,0.5l-2,4.3L0.7,5.5C0.4,5.5,0.1,5.8,0,6.1
		C-0.1,6.4,0,6.8,0.2,7l3.3,3.3L2.7,15c-0.1,0.3,0.1,0.7,0.3,0.9c0.3,0.2,0.6,0.2,0.9,0.1l4-2.2l4,2.2c0.1,0.1,0.3,0.1,0.4,0.1
		c0.2,0,0.4-0.1,0.5-0.2c0.3-0.2,0.4-0.5,0.3-0.9l-0.8-4.7L15.6,7c0.2-0.2,0.3-0.6,0.2-0.9C15.8,5.7,15.5,5.5,15.2,5.5z"/>
</g>
<g>
	<linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="-8.125" y1="10" x2="111.7512" y2="10">
		<stop  offset="0" style="stop-color:#115BC4"/>
		<stop  offset="1" style="stop-color:#05BDFD"/>
	</linearGradient>
	<path class="st1" d="M24,7.7c0-0.9,0.4-1.7,1.2-2.4c0.8-0.6,1.8-1,3.1-1s2.3,0.3,3,1c0.8,0.6,1.2,1.5,1.2,2.6h-2.3
		c0-0.5-0.2-1-0.6-1.3c-0.3-0.3-0.8-0.5-1.4-0.5s-1.1,0.1-1.4,0.4s-0.5,0.6-0.5,1.1c0,0.4,0.2,0.8,0.7,1c0.4,0.3,1,0.4,1.6,0.6
		c0.6,0.1,1.3,0.3,1.9,0.4c0.6,0.2,1.2,0.5,1.6,1c0.4,0.5,0.7,1.1,0.7,1.9c0,1-0.4,1.7-1.2,2.3s-1.8,0.9-3.1,0.9s-2.3-0.3-3.1-0.9
		c-0.8-0.6-1.2-1.5-1.3-2.6h2.4c0,0.5,0.2,1,0.6,1.3s0.8,0.5,1.5,0.5s1.1-0.1,1.5-0.4c0.4-0.3,0.6-0.6,0.6-1.1s-0.2-0.8-0.7-1.1
		c-0.4-0.3-1-0.4-1.6-0.6c-0.6-0.1-1.3-0.3-1.9-0.4c-0.6-0.2-1.2-0.5-1.6-0.9C24.2,9,24,8.4,24,7.7z"/>
	<linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="-8.125" y1="8.7" x2="111.7512" y2="8.7">
		<stop  offset="0" style="stop-color:#115BC4"/>
		<stop  offset="1" style="stop-color:#05BDFD"/>
	</linearGradient>
	<path class="st2" d="M40.5,6.4h-2.7v6.1c0,0.4,0.1,0.7,0.3,0.9s0.5,0.3,1,0.3h1.4v1.9h-1.8c-2.1,0-3.2-1-3.2-3.1V6.4h-1.3V4.5h1.3
		V1.8h2.3v2.7h2.7V6.4z"/>
	<linearGradient id="SVGID_4_" gradientUnits="userSpaceOnUse" x1="-8.125" y1="10" x2="111.7512" y2="10">
		<stop  offset="0" style="stop-color:#115BC4"/>
		<stop  offset="1" style="stop-color:#05BDFD"/>
	</linearGradient>
	<path class="st3" d="M43.4,14.1c-1-1.1-1.5-2.4-1.5-4.1s0.5-3,1.5-4.1c1-1,2.2-1.6,3.7-1.6c0.9,0,1.6,0.2,2.3,0.6s1.2,0.9,1.5,1.5
		v-2h2.3v11h-2.3v-1.9c-0.4,0.7-0.9,1.2-1.5,1.6c-0.7,0.4-1.4,0.6-2.3,0.6C45.6,15.7,44.4,15.2,43.4,14.1z M49.9,12.7
		c0.7-0.7,1-1.6,1-2.7s-0.3-2-1-2.7s-1.4-1-2.3-1s-1.7,0.3-2.3,1s-1,1.5-1,2.7c0,1.1,0.3,2,1,2.7s1.4,1,2.3,1
		C48.4,13.8,49.2,13.4,49.9,12.7z"/>
	<linearGradient id="SVGID_5_" gradientUnits="userSpaceOnUse" x1="-8.125" y1="10.05" x2="111.7512" y2="10.05">
		<stop  offset="0" style="stop-color:#115BC4"/>
		<stop  offset="1" style="stop-color:#05BDFD"/>
	</linearGradient>
	<path class="st4" d="M58.5,4.5v2c0.7-1.4,1.8-2.1,3.3-2.1v2.4h-0.6c-0.9,0-1.6,0.2-2,0.7s-0.7,1.2-0.7,2.4v5.8h-2.3V4.5H58.5z"/>
	<linearGradient id="SVGID_6_" gradientUnits="userSpaceOnUse" x1="-8.125" y1="8.65" x2="111.7512" y2="8.65">
		<stop  offset="0" style="stop-color:#115BC4"/>
		<stop  offset="1" style="stop-color:#05BDFD"/>
	</linearGradient>
	<path class="st5" d="M73.4,15.6l-1.1-3h-6.2l-1.1,3h-1.9l5-13.9h2l5,13.9H73.4z M66.7,11.1h5.1l-2.6-7.2L66.7,11.1z"/>
	<linearGradient id="SVGID_7_" gradientUnits="userSpaceOnUse" x1="-8.125" y1="8.25" x2="111.7512" y2="8.25">
		<stop  offset="0" style="stop-color:#115BC4"/>
		<stop  offset="1" style="stop-color:#05BDFD"/>
	</linearGradient>
	<path class="st6" d="M78.5,14.1C77.5,13,77,11.7,77,10s0.5-3,1.5-4.1c1-1,2.2-1.6,3.8-1.6c0.9,0,1.8,0.2,2.5,0.7s1.2,1,1.6,1.7v-6
		h1.8v14.8h-1.8v-2.2c-0.4,0.7-0.9,1.3-1.6,1.8c-0.7,0.4-1.5,0.7-2.5,0.7C80.7,15.7,79.5,15.2,78.5,14.1z M85.1,13.1
		c0.7-0.7,1.1-1.7,1.1-3c0-1.2-0.4-2.2-1.1-3C84.4,6.4,83.5,6,82.5,6s-1.9,0.4-2.6,1.1s-1.1,1.7-1.1,2.9s0.4,2.2,1.1,3
		s1.6,1.2,2.6,1.2C83.6,14.2,84.4,13.8,85.1,13.1z"/>
	<linearGradient id="SVGID_8_" gradientUnits="userSpaceOnUse" x1="-8.125" y1="10.05" x2="111.7512" y2="10.05">
		<stop  offset="0" style="stop-color:#115BC4"/>
		<stop  offset="1" style="stop-color:#05BDFD"/>
	</linearGradient>
	<path class="st7" d="M92.9,4.6v1.8c0.7-1.3,1.9-2,3.6-2c0.8,0,1.6,0.2,2.3,0.6c0.7,0.4,1.2,1,1.5,1.8c0.4-0.7,0.9-1.3,1.6-1.7
		c0.7-0.4,1.5-0.6,2.4-0.6c1.2,0,2.3,0.4,3.1,1.2c0.8,0.8,1.2,2,1.2,3.5v6.5h-1.8V9.5c0-1.1-0.3-1.9-0.8-2.5s-1.3-0.9-2.2-0.9
		s-1.7,0.3-2.2,0.9s-0.8,1.4-0.8,2.5v6.2H99V9.5c0-1.1-0.3-1.9-0.8-2.5S96.9,6.1,96,6.1S94.3,6.4,93.8,7S93,8.4,93,9.5v6.2h-1.8v-11
		L92.9,4.6L92.9,4.6z"/>
	<linearGradient id="SVGID_9_" gradientUnits="userSpaceOnUse" x1="-8.125" y1="7.95" x2="111.7512" y2="7.95">
		<stop  offset="0" style="stop-color:#115BC4"/>
		<stop  offset="1" style="stop-color:#05BDFD"/>
	</linearGradient>
	<path class="st8" d="M113.3,2.5c-0.2,0.2-0.5,0.4-0.9,0.4s-0.6-0.1-0.9-0.4c-0.2-0.2-0.3-0.5-0.3-0.9s0.1-0.6,0.4-0.9
		c0.2-0.2,0.5-0.4,0.9-0.4s0.6,0.1,0.9,0.4c0.2,0.2,0.4,0.5,0.4,0.9S113.5,2.2,113.3,2.5z M111.5,15.6v-11h1.8v11H111.5z"/>
	<linearGradient id="SVGID_10_" gradientUnits="userSpaceOnUse" x1="-8.125" y1="10" x2="111.7512" y2="10">
		<stop  offset="0" style="stop-color:#115BC4"/>
		<stop  offset="1" style="stop-color:#05BDFD"/>
	</linearGradient>
	<path class="st9" d="M118.1,4.6v1.8c0.7-1.3,1.9-2,3.6-2c1.2,0,2.2,0.4,3,1.2s1.2,2,1.2,3.5v6.5h-1.8V9.4c0-1.1-0.3-1.9-0.8-2.5
		S122,6,121.1,6s-1.7,0.3-2.2,0.9s-0.8,1.4-0.8,2.5v6.2h-1.8v-11C116.3,4.6,118.1,4.6,118.1,4.6z"/>
</g>
<polygon class="st10" points="25.1,-43.6 25.1,-43.6 25.1,-43.6 25.1,-43.6 "/>
</svg>
