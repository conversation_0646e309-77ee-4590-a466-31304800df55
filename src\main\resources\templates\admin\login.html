<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title><PERSON><PERSON><PERSON> nhập Admin</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- External CSS -->
    <link rel="stylesheet" th:href="@{/css/admin-login.css}">
    <link rel="stylesheet" th:href="@{/css/login-error.css}">
</head>
<body>
<!-- Animated background particles -->
<div class="particles">
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
</div>

<div class="login-container">
    <div class="login-header">
        <img th:src="@{/images/logo-w.png}" alt="Logo" style="max-width: 240px; height: auto;">
        <h1>Admin Login</h1>
        <p><PERSON><PERSON><PERSON> nhập vào hệ thống quản trị</p>
    </div>

    <form th:action="@{/admin/dang-nhap}" method="post" id="loginForm">
        <div class="form-group">
            <label for="username">Email</label>
            <input type="email" id="username" name="username" placeholder="Nhập email của bạn" required />
        </div>

        <div class="form-group">
            <label for="password">Mật khẩu</label>
            <input type="password" id="password" name="password" placeholder="Nhập mật khẩu" required />
        </div>

        <!-- ✅ CSRF token bắt buộc -->
        <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />

        <button type="submit" class="login-btn">
            Đăng nhập
        </button>
    </form>
    <div th:if="${param.error}" class="login-error-message">
        <span class="error-text" th:text="${param.error}"></span>
    </div>
</div>


<script th:src="@{/js/admin-login.js}"></script>
</body>
</html>
