<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{admin/layout}">
<head>
    <title>Admin pannel</title>
</head>
<body>
<section layout:fragment="content">
    <form method="get" th:action="@{/admin/thong-ke}">
        <div class="row mb-3">
            <!-- Ch<PERSON>n loại thời gian -->
            <div class="col-md-3">
                <select class="form-control" id="typeCalendarSelect">
                    <option value="ngay" th:selected="${typeCalendar == 'ngay'}">Theo ngày</option>
                    <option value="tuan" th:selected="${typeCalendar == 'tuan'}">Theo tuần</option>
                    <option value="thang" th:selected="${typeCalendar == 'thang'}">Theo tháng</option>
                    <option value="nam" th:selected="${typeCalendar == 'nam'}">Theo năm</option>
                    <option value="custom" th:selected="${typeCalendar == 'custom'}">Tùy chỉnh</option>
                </select>
            </div>

            <!-- <PERSON><PERSON><PERSON> bắt đầu -->
            <div class="col-md-3">
                <input type="date" class="form-control" name="startDate"
                       th:value="${#temporals.format(startDate, 'yyyy-MM-dd')}" id="startDateInput" />
            </div>

            <!-- Ngày kết thúc -->
            <div class="col-md-3">
                <input type="date" class="form-control" name="endDate"
                       th:value="${#temporals.format(endDate, 'yyyy-MM-dd')}" id="endDateInput" />
            </div>

            <!-- Submit -->
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary w-100">Lọc</button>
            </div>
        </div>

        <!-- Ẩn trường này để set về custom nếu chọn ngày -->
        <input type="hidden"  name="typeCalendar" value="custom" id="typeCalendarHidden"/>
    </form>

    <div class="row">
        <!-- Doanh thu -->
        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6 grid-margin stretch-card">
            <div class="card card-statistics">
                <div class="card-body">
                    <div class="clearfix">

                        <div class="float-left">
                            <i class="mdi mdi-check-circle-outline text-primary icon-lg"></i>
                        </div>
                        <div class="float-right">
                            <p class="mb-0 text-right">Đơn thành công</p>
                            <h3 class="font-weight-medium text-right mb-0" th:text="${donThanhCong}"></h3>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <i class="mdi mdi-calendar mr-1"></i> Thống kê [[${labelThoiGian}]]
                    </p>
                </div>
            </div>
        </div>

        <!-- Đơn thành công -->
        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6 grid-margin stretch-card">
            <div class="card card-statistics">
                <div class="card-body">
                    <div class="clearfix">
                        <div class="float-left">
                            <i class="mdi mdi-close-circle text-danger icon-lg"></i>
                        </div>
                        <div class="float-right">
                            <p class="mb-0 text-right">Đơn trả</p>
                            <h3 class="font-weight-medium text-right mb-0" th:text="${donTra}"></h3>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <i class="mdi mdi-check mr-1"></i> [[${labelThoiGian}]]
                    </p>
                </div>
            </div>
        </div>

        <!-- Đơn hủy -->
        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6 grid-margin stretch-card">
            <div class="card card-statistics">
                <div class="card-body">
                    <div class="clearfix">
                        <div class="float-left">
                            <i class="mdi mdi-close-circle text-danger icon-lg"></i>
                        </div>
                        <div class="float-right">
                            <p class="mb-0 text-right">Đơn hủy</p>
                            <h3 class="font-weight-medium text-right mb-0" th:text="${donHuy}"></h3>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <i class="mdi mdi-cancel mr-1"></i> [[${labelThoiGian}]]
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <!-- Đơn trả -->
        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 grid-margin stretch-card">
            <div class="card card-statistics">
                <div class="card-body">
                    <div class="clearfix">
                        <div class="float-left">
                            <i class="mdi mdi-cash-multiple text-success icon-lg"></i>
                        </div>
                        <div class="float-right">
                            <p class="mb-0 text-right">Doanh thu</p>
                            <h3 class="font-weight-medium text-right mb-0" th:text="${@utils.formatCurrency(doanhThu)}"></h3>
                        </div>

                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <i class="mdi mdi-cancel mr-1"></i> [[${labelThoiGian}]]
                    </p>
                </div>
            </div>
        </div>

        <!-- Tổng sản phẩm -->
        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 grid-margin stretch-card">
            <div class="card card-statistics">
                <div class="card-body">
                    <div class="clearfix">
                        <div class="float-left">
                            <i class="mdi mdi-cube-outline text-warning icon-lg"></i>
                        </div>
                        <div class="float-right">
                            <p class="mb-0 text-right">Sản phẩm</p>
                            <h3 class="font-weight-medium text-right mb-0" th:text="${tongSanPham}"></h3>
                        </div>
                    </div>
                    <p class="text-muted mt-3 mb-0">
                        <i class="mdi mdi-package-variant mr-1"></i> Tính đến hiện tại
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12 grid-margin">
            <div class="card">
                <div class="card-body">
                    <h4 class="card-title">Orders</h4>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <th>
                                    #
                                </th>
                                <th>
                                    Sản phẩm
                                </th>
                                <th>
                                    Đã bán
                                </th>
                                <th>
                                    Số lượng tồn
                                </th>
<!--                                <th>-->
<!--                                    Giá bán-->
<!--                                </th>-->
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="sp,index : ${sanPhamThongKeList}"
                                th:classappend="${sp.soLuongTon == 0} ? 'bg-danger-subtle' : ''">
                                <td th:text="${index.count}"></td>
                                <td th:text="${sp.tenCt}"></td>
                                <td th:text="${sp.soLuongDaBan}"></td>
                                <td th:text="${sp.soLuongTon}"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <script>
        const typeCalendarSelect = document.getElementById("typeCalendarSelect");
        const typeCalendarHidden = document.getElementById("typeCalendarHidden");
        const startDateInput = document.getElementById("startDateInput");
        const endDateInput = document.getElementById("endDateInput");

        // Khi chọn ngày/tháng/năm/... từ dropdown
        typeCalendarSelect.addEventListener("change", function () {
            const today = new Date();
            let startDate, endDate;

            switch (this.value) {
                case "ngay":
                    startDate = endDate = today;
                    break;
                case "tuan":
                    const dayOfWeek = today.getDay(); // 0: CN, 1: T2, ..., 6: T7
                    const diffToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
                    startDate = new Date(today);
                    startDate.setDate(today.getDate() + diffToMonday);
                    endDate = new Date(startDate);
                    endDate.setDate(startDate.getDate() + 6);
                    break;
                case "thang":
                    startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                    endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                    break;
                case "nam":
                    startDate = new Date(today.getFullYear(), 0, 1);
                    endDate = new Date(today.getFullYear(), 11, 31);
                    break;
                case "custom":
                    // Không làm gì
                    return;
            }

            // Format yyyy-MM-dd
            startDateInput.value = startDate.toISOString().split('T')[0];
            endDateInput.value = endDate.toISOString().split('T')[0];

            // Đồng bộ hidden input với giá trị select
            typeCalendarHidden.value = this.value;
        });

        // Khi chọn ngày thủ công => chuyển dropdown sang custom
        startDateInput.addEventListener("change", function () {
            typeCalendarSelect.value = "custom";
            typeCalendarHidden.value = "custom";
        });
        endDateInput.addEventListener("change", function () {
            typeCalendarSelect.value = "custom";
            typeCalendarHidden.value = "custom";
        });
    </script>





</section>
</body>
</html>
