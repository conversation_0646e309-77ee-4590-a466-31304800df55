package com.main.datn_sd31.controller.admin_controller;

import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
public class CustomErrorController implements ErrorController {

    private static final Logger logger = LoggerFactory.getLogger(CustomErrorController.class);

    @RequestMapping("/error")
    public String handleError(HttpServletRequest request) {
        // Lấy thông tin từ request
        String referer = request.getHeader("Referer");
        String requestURI = (String) request.getAttribute("jakarta.servlet.forward.request_uri");
        String originalURI = (String) request.getAttribute("jakarta.servlet.error.request_uri");

        // <PERSON><PERSON>m tra URL gốc gây ra lỗi
        if (originalURI != null) {
            if (originalURI.startsWith("/admin")) {
                return "admin/error/404";
            }
            if (originalURI.startsWith("/khach-hang")) {
                return "client/error/404";
            }
        }

        // Kiểm tra request URI hiện tại
        if (requestURI != null) {
            if (requestURI.startsWith("/admin")) {
                return "admin/error/404";
            }
            if (requestURI.startsWith("/khach-hang")) {
                return "client/error/404";
            }
        }

        // Kiểm tra referer (trang trước đó)
        if (referer != null) {
            if (referer.contains("/admin")) {
                return "admin/error/404";
            }
            if (referer.contains("/khach-hang")) {
                return "client/error/404";
            }
        }

        // Mặc định trả về trang lỗi chung cho public
        return "error/404";
    }

}