<!DOCTYPE html>
<!--Cấu hình layout cho Thymleaf-->
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{admin/layout}">
<head>
    <title>Voucher</title>
    <!--    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet"-->
    <!--          integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT" crossorigin="anonymous">-->

    <!--    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">-->
    <style>
        .filter-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .filter-buttons .btn {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .table-section {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
            overflow-x: hidden;
        }

        .table-section .table {
            width: 100%;
            table-layout: fixed;
            border-collapse: collapse;
        }

        .table-section .table th,
        .table-section .table td {
            white-space: normal; /* Cho phép xuống dòng */
            overflow-wrap: break-word; /* Tự động xuống dòng nếu từ quá dài */
            word-break: break-word; /* Ngắt từ nếu quá dài */
            max-width: 200px; /* (Tùy chọn) Giới hạn độ rộng mỗi ô để buộc xuống dòng */
            vertical-align: middle;
            padding: 8px;
        }

        .table-section .table thead th {
            background-color: #f1f1f1;
        }

        .action-btn {
            border: none;
            background: none;
            cursor: pointer;
            font-size: 1.2rem;
            padding: 5px;
        }

        .action-btn.accept {
            color: green;
        }

        .action-btn.cancel {
            color: red;
        }

        .disabled-custom {
            pointer-events: none;      /* không cho click */
            opacity: 0.5;              /* mờ đi */
            cursor: not-allowed;       /* con trỏ chuột kiểu không được phép */
        }
    </style>
</head>
<body>

<section layout:fragment="content">
    <div class="container mt-4">
        <h2 class="mb-4 text-center">Quản Lý Phiếu giảm giá</h2>
        <a th:href="@{/admin/phieu-giam-gia/create}" class="btn btn-success mb-3">
            + Thêm mới
        </a>

        <!-- Filter Section -->
        <form
                th:action="@{/admin/phieu-giam-gia}"
                method="get"
                class="filter-section"
        >
            <div class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label for="startDate" class="form-label">Ngày bắt đầu</label>
                    <input
                            type="date"
                            class="form-control"
                            id="startDate"
                            name="startDate"
                            th:value="${startDate}"
                    />
                </div>
                <div class="col-md-3">
                    <label for="endDate" class="form-label">Ngày kết thúc</label>
                    <input
                            type="date"
                            class="form-control"
                            id="endDate"
                            name="endDate"
                            th:value="${endDate}"
                    />
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Trạng thái</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">-- Tất cả --</option>
                        <option value="hoatdong" th:selected="${status == 'hoatdong'}">
                            Đang hoạt động
                        </option>
                        <option value="chuabatdau" th:selected="${status == 'chuabatdau'}">
                            Đang chờ
                        </option>
                        <option value="ketthuc" th:selected="${status == 'ketthuc'}">
                            Kết thúc
                        </option>
                        <option value="hethang" th:selected="${status == 'hethang'}">
                            Sử dụng hết
                        </option>
                    </select>
                </div>
                <div class="col-md-3 d-flex gap-2">
                    <button type="submit" class="btn btn-primary w-100">Tìm kiếm</button>
                    <a
                            th:href="@{/admin/phieu-giam-gia}"
                            class="btn btn-outline-secondary w-100"
                    >Làm mới</a
                    >
                </div>
            </div>
        </form>

        <!-- Table Section -->
        <div class="table-section">
            <table
                    class="table table-bordered"
                    id="phieuGiamGiaTable"
                    style="width: 100%"
            >
                <thead>
                <tr>
                    <th>STT</th>
                    <th>Thông tin chung</th>
                    <th>Loại phiếu</th>
                    <th>Mức giảm</th>
                    <th>Giảm tối đa</th>
                    <th>Điều kiện</th>
                    <th>Số lượng</th>
                    <th>Ngày bắt đầu</th>
                    <th>Ngày kết thúc</th>
                    <th>Trạng thái</th>
                    <th>Thao Tác</th>
                </tr>
                </thead>
                <tbody>
                <tr th:each="item, iterStat : ${listData}">
                    <td th:text="${iterStat.index + 1}"></td>
                    <td>
                        <div class="text-sm text-gray-500" th:text="${item.ma}"></div>
                        <div class="text-sm text-gray-900" th:text="${item.ten}"></div>
                    </td>
                    <td
                            th:text="${item.loaiPhieuGiamGia == 1 ? 'Giảm %' : 'Giảm tiền'}"
                    ></td>
                    <td
                            th:text="${item.loaiPhieuGiamGia == 1 ? item.mucDo.stripTrailingZeros().toPlainString() + '%' : item.mucDo.stripTrailingZeros().toPlainString() + ' đ'}"
                    ></td>
                    <td
                            th:text="${item.giamToiDa.stripTrailingZeros().toPlainString() + ' đ'}"
                    ></td>
                    <td
                            th:text="${'>= ' + item.dieuKien.stripTrailingZeros().toPlainString() + ' đ'}"
                    ></td>
                    <td
                            th:text="${item.soLuongTon != null ? item.soLuongTon : 0}"
                    ></td>
                    <td th:text="${item.ngayBatDau}"></td>
                    <td th:text="${item.ngayKetThuc}"></td>
                    <td>
                <span th:if="${item.soLuongTon == 0}" class="badge bg-secondary"
                >Sử dụng hết</span
                >
                        <span
                                th:if="${item.ngayBatDau > T(java.time.LocalDate).now() and item.soLuongTon > 0}"
                                class="badge bg-warning text-dark"
                        >Đang chờ</span
                        >
                        <span
                                th:if="${item.ngayKetThuc < T(java.time.LocalDate).now() and item.soLuongTon > 0}"
                                class="badge bg-danger"
                        >Kết thúc</span
                        >
                        <span
                                th:if="${item.ngayBatDau <= T(java.time.LocalDate).now() and item.ngayKetThuc >= T(java.time.LocalDate).now() and item.soLuongTon > 0}"
                                class="badge bg-success"
                        >Đang hoạt động</span
                        >
                    </td>
                    <td>
                        <a
                                th:href="@{/admin/phieu-giam-gia/edit/{id}(id=${item.id})}"
                                class="btn btn-primary btn-sm"
                        >
                            Sửa
                        </a>
                        <a th:href="@{'/admin/phieu-giam-gia/delete/' + ${item.id}}"
                           th:classappend="${@phieu_giam_gia_utils.khongChoPhepXoaPhieuGiamGia(item.id) ? ' disabled-custom' : ''}"
                           th:attr="title=${@phieu_giam_gia_utils.khongChoPhepXoaPhieuGiamGia(item.id) ? 'Không thể xóa' : null}"
                           onclick="return !@phieu_giam_gia_utils.khongChoPhepXoaPhieuGiamGia(item.id) ? confirm('Bạn có chắc muốn xóa phiếu này không?') : false"
                           class="btn btn-danger btn-sm">
                            Xóa
                        </a>

                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</section>

<script>
    // ✅ Định nghĩa hàm resetForm là global để dùng được trong onclick
    window.resetForm = function () {
        const form = document.getElementById("phieuGiamGiaForm");
        if (form) form.reset();

        // Xóa giá trị hidden ID nếu có
        const hiddenId = document.querySelector("input[name='id']");
        if (hiddenId) hiddenId.value = "";

        // Đặt lại placeholder và ẩn/hiện giảm tối đa
        document.getElementById("mucDo").placeholder = "";
        document.getElementById("giamToiDa").value = 0;
        document.getElementById("divGiamToiDa").style.display = "block";

        // Đổi action về create
        document.getElementById("phieuGiamGiaForm").setAttribute("action", "/admin/phieu-giam-gia/create");

        // Hiển thị modal
        const modal = new bootstrap.Modal(document.getElementById("createModal"));
        modal.show();
    };

    // ✅ Phần chạy sau khi DOM đã load
    $(document).ready(function () {
        // Kích hoạt datatables
        $(".table").DataTable({
            language: {
                url: "https://cdn.datatables.net/plug-ins/1.13.4/i18n/vi.json",
            },
            pageLength: 10,
            lengthMenu: [10, 25, 50, 100],
        });

        // Submit form ajax
        $("#phieuGiamGiaForm").on("submit", function (e) {
            e.preventDefault();
            const form = $(this);
            $.ajax({
                type: "POST",
                url: form.attr("action"),
                data: form.serialize(),
                success: function (response) {
                    if (response.success) {
                        alert("Lưu thành công!");
                        location.reload();
                    } else {
                        alert("Có lỗi xảy ra: " + response.message);
                    }
                },
                error: function () {
                    alert("Có lỗi xảy ra khi lưu dữ liệu.");
                },
            });
        });

        // Bắt sự kiện khi click nút sửa
        $(".btn-edit").on("click", function (e) {
            e.preventDefault();
            const id = $(this).data("id");
            $.ajax({
                url: "/admin/phieu-giam-gia/get/" + id,
                method: "GET",
                success: function (data) {
                    const form = $("#phieuGiamGiaForm");

                    // Gán action update
                    form.attr("action", "/admin/phieu-giam-gia/update");

                    // Gán dữ liệu từ response vào form
                    for (let field in data) {
                        form.find("[name='" + field + "']").val(data[field]);
                    }

                    // Trigger lại loại phiếu để xử lý hiển thị giảm tối đa
                    $("#loaiPhieuGiamGia").trigger("change");

                    const modal = new bootstrap.Modal(document.getElementById("createModal"));
                    modal.show();
                },
                error: function () {
                    alert("Không thể lấy dữ liệu phiếu giảm giá.");
                },
            });
        });

        // Xử lý hiển thị giảm tối đa theo loại phiếu
        $("#loaiPhieuGiamGia").on("change", function () {
            const selectedValue = $(this).val();
            if (selectedValue === "GIAM_PERCENT") {
                $("#mucDo").attr("placeholder", "Nhập phần trăm giảm giá");
                $("#divGiamToiDa").show();
                $("#giamToiDa").attr("required", true).val(0);
            } else if (selectedValue === "GIAM_TIEN") {
                $("#mucDo").attr("placeholder", "Nhập số tiền giảm");
                $("#divGiamToiDa").hide();
                $("#giamToiDa").removeAttr("required").val(0);
            }
        });
    });

    // ✅ Hiển thị modal tự động nếu có biến showCreateModal được set
    // Ví dụ được dùng khi redirect lại với trạng thái lỗi
</script>

<!-- Nếu sử dụng Thymeleaf để mở modal khi có lỗi -->
<script th:if="${showCreateModal}" th:inline="javascript">
    const createModal = new bootstrap.Modal(document.getElementById("createModal"));
    createModal.show();
</script>

<script>
    $(document).ready(function () {
        $(".table").DataTable({
            language: {
                url: "https://cdn.datatables.net/plug-ins/1.13.4/i18n/vi.json",
            },
            pageLength: 10,
            lengthMenu: [10, 25, 50, 100],
        });
    });
</script>

</body>
</html>