<!DOCTYPE html>
<html lang="vi" xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{admin/layout}">
<head>
    <meta charset="UTF-8">
    <title>B<PERSON> Hàng Trực <PERSON>ếp</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .card { border-radius: 10px; margin-bottom: 20px; }
        .total { font-size: 1.2rem; font-weight: bold; color: #e74c3c; }
    </style>
</head>
<body>
<section layout:fragment="content">
    <div class="container-fluid mt-5">
        <div class="row">

            <h2 class="text-center mb-4"><PERSON><PERSON>ếp</h2>

            <!-- Tabs Giỏ hàng -->


            <div class="col-md-6">
                <!-- Chọn sản phẩm -->
                <div class="card p-3">
                    <form id="formThemGio" method="post" th:action="@{/admin/ban-hang/them-gio}">
                        <input type="hidden" name="cartKey" th:value="${cartKey}" />
                        <input type="hidden" name="idChiTietSp" id="idChiTietSp" />
                        <div class="row g-2">
                            <!-- Tìm kiếm sản phẩm -->
                            <div class="col-md-6 position-relative">
                                <input type="text" class="form-control" id="timSanPham" placeholder="Tìm theo tên sản phẩm..." autocomplete="off" />
                                <div id="goiYSanPham" class="list-group position-absolute w-100 z-3" style="max-height: 200px; overflow-y: auto;"></div>
                            </div>
                            <!-- Nhập số lượng -->
                            <div class="col-md-2">
                                <input type="number" name="soLuong" class="form-control" value="1" min="1" required />
                            </div>
                            <!-- Modal Quét Mã -->
                            <div class="modal fade" id="scannerModal" tabindex="-1" aria-labelledby="scannerModalLabel" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Quét mã sản phẩm</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div id="scanner" style="width: 100%; height: 400px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-2">
                                <button type="button" class="btn btn-outline-secondary w-100" onclick="openScanner()">📷 Quét mã</button>
                            </div>
                            <div th:if="${error}" class="alert alert-danger" role="alert">
                                <p th:text="${error}"></p>
                            </div>

                            <!-- Nút thêm vào giỏ -->
                            <!--                            <div class="col-md-2">-->
                            <!--                                <button type="submit" class="btn btn-primary w-100">Thêm vào giỏ</button>-->
                            <!--                            </div>-->
                        </div>
                    </form>
                </div>


                <!-- Chọn chi tiết sản phẩm -->
                <table class="table">
                    <thead>
                    <tr>
                        <th class="p-3">#</th>
                        <th class="p-3">Mã</th>
                        <th class="p-3">Ảnh</th>
                        <th class="p-3">Tên Sản phẩm</th>
                    </tr>
                    </thead>
                    <tbody id="ketQuaTimSanPham">
                    <tr th:each="sp, stat : ${dsSanPham}">
                        <td class="p-3" th:text="${stat.index + 1}">1</td>
                        <td class="p-3" th:text="${sp.ma}">SP001</td>
                        <td class="p-3">
                            <img th:src="@{'/uploads/' + ${sp.hinhAnhs[0].url}}" alt="Ảnh" class="w-12 h-12 object-cover rounded">
                        </td>
                        <td class="p-3" th:text="${sp.ten}">SP001</td>
                    </tr>
                    </tbody>
                </table>

            </div>
            <!-- Giỏ hàng -->
            <div class="col-md-6">
                <ul class="nav nav-tabs mb-3">
                    <li class="nav-item d-flex align-items-center" th:each="key, iterStat : ${tatCaGio}">
                        <a class="nav-link d-flex align-items-center" th:classappend="${key == cartKey} ? 'active'"
                           th:href="@{/admin/ban-hang(cartKey=${key})}">
                            <span th:text="'Giỏ ' + ${iterStat.index + 1}">Giỏ</span>
                            <form th:action="@{/admin/ban-hang/xoa-gio}" method="post" class="ms-2 mb-0" style="display:inline;">
                                <input type="hidden" name="cartKey" th:value="${key}" />
                                <button type="submit" class="btn btn-sm btn-close" title="Xóa giỏ này" style="margin-left: 8px;"></button>
                            </form>

                        </a>
                        <!-- Nút X để xóa giỏ -->

                    </li>

                    <!-- Thêm mới nếu chưa đủ 10 giỏ -->
                    <li class="nav-item" th:if="${tatCaGio.size() < 10}">
                        <a class="nav-link text-success"
                           th:href="@{/admin/ban-hang(cartKey='gio-' + ${tatCaGio.size() + 1})}">
                            + Giỏ mới
                        </a>
                    </li>
                </ul>
                <div class="card p-3">
                    <h4>Giỏ hàng</h4>
                    <table class="table table-bordered">
                        <thead class="table-light">
                        <tr>
                            <th>Sản phẩm</th>
                            <th>Giá</th>
                            <th>Số lượng</th>
                            <th>Thành tiền</th>
                            <th>Hành Động</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr th:each="item : ${gioHang}">
                            <td th:text="${item.tenCtsp}"></td>
                            <td th:text="${#numbers.formatDecimal(item.giaSauGiam, 0, 'POINT', 0, 'COMMA')} + ' ₫'"></td>
                            <td>
                                <form th:action="@{/admin/ban-hang/cap-nhat-so-luong}" method="post"
                                      onsubmit="return validateSoLuong(this)">
                                    <input type="hidden" name="idChiTietSp" th:value="${item.chiTietSanPham.id}" />
                                    <input type="hidden" name="cartKey" th:value="${cartKey}" />
                                    <input type="number" name="soLuong"
                                           th:value="${item.soLuong}"
                                           min="1"
                                           th:attr="max=${item.chiTietSanPham.soLuong}"
                                           class="form-control so-luong-input"
                                           onchange="this.form.submit()"
                                           required />
                                </form>
                            </td>

                            <td th:text="${#numbers.formatDecimal(item.giaSauGiam * item.soLuong, 0, 'POINT', 0, 'COMMA')} + ' ₫'"></td>
                            <td>
                                <form th:action="@{/admin/ban-hang/xoa-san-pham}" method="post" onsubmit="return confirm('Bạn có chắc muốn xóa sản phẩm này không?');">
                                    <input type="hidden" name="cartKey" th:value="${cartKey}" />
                                    <input type="hidden" name="idChiTietSp" th:value="${item.chiTietSanPham.id}" />
                                    <button type="submit" class="btn btn-sm btn-danger">Xóa</button>
                                </form>
                            </td>
                        </tr>
                        <tr th:if="${gioHang.isEmpty()}">
                            <td colspan="4" class="text-center text-muted">Chưa có sản phẩm nào</td>
                        </tr>
                        </tbody>
                    </table>


                    <p>Tổng tiền: <span class="total" th:text="${#numbers.formatDecimal(tongTien, 0, 'POINT', 0, 'COMMA')} + ' ₫'"></span></p>
                    <form method="post" th:action="@{/admin/ban-hang/xoa-gio}">
                        <input type="hidden" name="cartKey" th:value="${cartKey}" />
                        <button class="btn btn-danger">Xóa giỏ hàng</button>
                    </form>
                </div>

                <!-- Mã giảm giá -->
                <div class="card p-3">
                    <form method="post" th:action="@{/admin/ban-hang/ap-dung-ma}">
                        <input type="hidden" name="cartKey" th:value="${cartKey}" />
                        <div class="row g-2 mb-2">
                            <div class="col-md-6">
                                <select class="form-select" name="maGiamGia">
                                    <option value="">-- Mã giảm giá --</option>
                                    <option th:each="p : ${dsPhieuGiamGia}" th:value="${p.ma}"
                                            th:text="${p.ma + '-' + (#numbers.formatDecimal(p.mucDo, 0, 'COMMA', 0, 'POINT')) + (p.loaiPhieuGiamGia == 1 ? '%' : 'đ') + ' - giảm tối đa:' + (#numbers.formatDecimal(p.giamToiDa, 0, 'COMMA', 0, 'POINT'))}">
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-secondary" type="submit">Áp dụng mã</button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Thanh toán -->
                <div class="card p-3">
                    <form method="post" th:action="@{/admin/ban-hang/thanh-toan}" onsubmit="updateDiaChiHienThi()">
                        <input type="hidden" name="giagiam" th:value="${giamGia}" />
                        <input type="hidden" name="diaChiTinh" id="hiddenTinh">
                        <input type="hidden" name="diaChiHuyen" id="hiddenHuyen">
                        <input type="hidden" name="diaChiXa" id="hiddenXa">

                        <!-- Checkbox để bật/tắt tính năng vận chuyển -->
                        <div class="form-check my-3">
                            <input class="form-check-input" type="checkbox" id="muonVanChuyen">
                            <label class="form-check-label fw-bold" for="muonVanChuyen">
                                🚚 Tôi muốn giao hàng tới địa chỉ
                            </label>
                        </div>

                        <!-- Địa chỉ giao hàng -->
                        <div id="diaChiVanChuyen" style="display: none;">
                            <h5>Địa chỉ giao hàng</h5>
                            <div class="row g-2 mb-3">
                                <div class="col-md-4"><select id="province" class="form-select" name="province"></select></div>
                                <div class="col-md-4"><select id="district" class="form-select" name="district"></select></div>
                                <div class="col-md-4"><select id="ward" class="form-select" name="ward"></select></div>
                            </div>

                            <div class="mb-3">
                                <label for="soDienThoai" class="form-label">Số điện thoại:</label>
                                <input type="text" name="soDienThoai" class="form-control" id="soDienThoai" placeholder="Nhập số điện thoại">
                            </div>

                            <!-- Nút tạo đơn vận chuyển -->

                        </div>

                        <div th:if="${maGiamGia != null}">
                            <p>Mã giảm giá đã áp dụng: <strong th:text="${maGiamGia}"></strong></p>
                        </div>

                        <div>
                            <p>Phí vận chuyển: <strong id="shippingFee" th:text="${phiVanChuyen != null ? #numbers.formatDecimal(phiVanChuyen, 0, 'POINT', 0, 'COMMA') + ' ₫' : 'Chưa tính'}">Chưa tính</strong></p>
                            <p>Tổng tiền: <span class="total" th:text="${#numbers.formatDecimal(tongTien, 0, 'POINT', 0, 'COMMA')} + ' ₫'"></span></p>
                            <p th:if="${giamGia != null and giamGia > 0}">
                                Giảm giá: <span class="total" th:text="${#numbers.formatDecimal(giamGia, 0, 'POINT', 0, 'COMMA')} + ' ₫'"></span>
                            </p>
                            <p>Thành tiền sau giảm: <span class="total" id="tongTien" th:text="${#numbers.formatDecimal(tongTienSauGiam, 0, 'POINT', 0, 'COMMA')} + ' ₫'"></span></p>
                        </div>

                        <div class="d-flex justify-content-between align-items-start mb-3 flex-wrap">
                            <!-- Cột trái: phương thức thanh toán -->
                            <div class="me-4">
                                <label class="form-label fw-bold">Phương thức thanh toán <span class="text-danger">*</span></label>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="radio" name="phuongThucThanhToan" id="pttt_cod" value="tien_mat" required onchange="toggleQrImage()">
                                    <label class="form-check-label" for="pttt_cod">💵 Tiền mặt khi nhận hàng (COD)</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="phuongThucThanhToan" id="pttt_ck" value="chuyen_khoan" onchange="toggleQrImage()">
                                    <label class="form-check-label" for="pttt_ck">🏦 Chuyển khoản ngân hàng</label>
                                </div>
                            </div>

                            <!-- Cột phải: ảnh QR -->
                            <div id="qrChuyenKhoan" style="display: none;">
                                <div class="text-center">
                                    <p class="fw-bold mb-2">Quét mã QR để chuyển khoản</p>
                                    <img src="/uploads/Qr.jpg" alt="QR chuyển khoản"
                                         style="max-width: 150px;" class="img-fluid border rounded">
                                </div>
                            </div>
                        </div>


                        <input type="hidden" name="cartKey" th:value="${cartKey}" />

                        <button class="btn btn-success w-100 mt-2">Thanh toán</button>
                    </form>
                    <!--                    <div class="mb-3">-->
                    <!--                        <button type="button" class="btn btn-outline-primary" id="btnTaoDonVanChuyen">🚀 Tạo đơn vận chuyển</button>-->
                    <!--                    </div>-->
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/quagga/0.12.1/quagga.min.js"></script>
    <script src="https://unpkg.com/quagga@0.12.1/dist/quagga.min.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", async function () {
            const provinceSelect = document.getElementById("province");
            const districtSelect = document.getElementById("district");
            const wardSelect = document.getElementById("ward");
            const shippingFeeElement = document.getElementById("shippingFee");

            // Hàm gọi API lấy danh sách tỉnh/huyện/xã
            async function fetchData(url) {
                const response = await fetch(url);
                if (!response.ok) throw new Error("Lỗi khi fetch dữ liệu");
                return await response.json();
            }

            // Load tỉnh khi vào trang
            const provinces = await fetchData("/admin/ban-hang/dia-chi/tinh");
            provinceSelect.innerHTML = '<option value="">-- Chọn tỉnh --</option>';
            provinces.forEach(p => {
                provinceSelect.innerHTML += `<option value="${p.ProvinceID}">${p.ProvinceName}</option>`;
            });

            // Khi chọn tỉnh → load huyện
            provinceSelect.addEventListener("change", async function () {
                const provinceId = this.value;
                districtSelect.innerHTML = '<option value="">-- Chọn huyện --</option>';
                wardSelect.innerHTML = '<option value="">-- Chọn xã --</option>';
                shippingFeeElement.innerText = "Chưa tính";
                if (!provinceId) return;

                const districts = await fetchData(`/admin/ban-hang/dia-chi/huyen?provinceId=${provinceId}`);
                districts.forEach(d => {
                    districtSelect.innerHTML += `<option value="${d.DistrictID}">${d.DistrictName}</option>`;
                });
            });

            // Khi chọn huyện → load xã
            districtSelect.addEventListener("change", async function () {
                const districtId = this.value;
                wardSelect.innerHTML = '<option value="">-- Chọn xã --</option>';
                shippingFeeElement.innerText = "Chưa tính";
                if (!districtId) return;

                const wards = await fetchData(`/admin/ban-hang/dia-chi/xa?districtId=${districtId}`);
                wards.forEach(w => {
                    wardSelect.innerHTML += `<option value="${w.WardCode}">${w.WardName}</option>`;
                });
            });

            // Khi chọn xã → gọi tính phí ship
            wardSelect.addEventListener("change", async function () {
                const districtId = districtSelect.value;
                const wardCode = this.value;
                if (!districtId || !wardCode) return;

                try {
                    const response = await fetch(`/admin/ban-hang/phi-ship?toDistrictId=${districtId}&wardCode=${wardCode}`);
                    const fee = await response.json();
                    shippingFeeElement.innerText = fee.toLocaleString("vi-VN") + " ₫";
                } catch (error) {
                    shippingFeeElement.innerText = "Không thể tính phí";
                    console.error("Lỗi khi tính phí vận chuyển:", error);
                }
            });
        });
    </script>

    <!-- Script -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/quagga/0.12.1/quagga.min.js"></script>

    <script>
        btnThemGioHang.addEventListener("click", () => {
            const idMau = chonMau.value;
            const idSize = chonSize.value;

            if (!idMau || !idSize || !idSanPhamDangChon) {
                alert("Vui lòng chọn đầy đủ màu sắc và size!");
                return;
            }

            // Gửi dữ liệu thêm giỏ hàng (POST)
            fetch("/admin/ban-hang/them-gio", {
                method: "POST",
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    idSanPham: idSanPhamDangChon,
                    idMauSac: idMau,
                    idSize: idSize
                })
            })
                .then(res => res.text())
                .then(msg => alert(msg));
        });



        document.addEventListener("click", function (e) {
            if (!goiYBox.contains(e.target) && e.target !== timSanPham) {
                goiYBox.innerHTML = "";
            }
        });

        function openScanner() {
            const modal = new bootstrap.Modal(document.getElementById("scannerModal"));
            modal.show();

            Quagga.init({
                inputStream: {
                    name: "Live",
                    type: "LiveStream",
                    target: document.querySelector('#scanner'),
                    constraints: {
                        width: 640,
                        height: 400,
                        facingMode: "environment" // camera sau
                    }
                },
                decoder: {
                    readers: ["ean_reader", "code_128_reader", "upc_reader", "ean_8_reader", "code_39_reader"]
                }
            }, function (err) {
                if (err) {
                    console.error("Quagga init error:", err);
                    return;
                }
                Quagga.start();
            });
            Quagga.onDetected(function (result) {
                const code = result.codeResult.code;
                Quagga.offDetected();
                Quagga.stop();
                modal.hide();

                fetch(`/admin/ban-hang/tim-kiem-theo-ma-vach?maVach=${code}`)
                    .then(response => response.json())
                    .then(sp => {
                        if (sp && sp.tenSanPham) {
                            timSanPham.value = `${sp.tenSanPham} - ${sp.mauSac} / ${sp.size} - SL: ${sp.soluong} - Giá: ${sp.gia}`;
                            document.getElementById("idChiTietSp").value = sp.idChiTietSp;
                        } else {
                            timSanPham.value = `Không tìm thấy mã vạch: ${code}`;
                        }
                    })
                    .catch(error => {
                        console.error("Lỗi khi tìm sản phẩm:", error);
                        timSanPham.value = "Lỗi kết nối khi tìm sản phẩm.";
                    });
            });
            Quagga.onDetected(function (result) {
                const code = result.codeResult.code;
                Quagga.offDetected();
                Quagga.stop();
                modal.hide();

                fetch(`/admin/ban-hang/tim-kiem-theo-ma-vach?maVach=${code}`)
                    .then(response => response.json())
                    .then(sp => {
                        if (sp && sp.tenSanPham) {
                            timSanPham.value = `${sp.tenSanPham} - ${sp.mauSac} / ${sp.size} - SL: ${sp.soluong} - Giá: ${sp.gia}`;
                            document.getElementById("idChiTietSp").value = sp.idChiTietSp;
                            const form = document.getElementById("formThemGio"); // gán id cho form của bạn
                            if (form) {
                                form.submit();
                            }
                        } else {
                            timSanPham.value = `Không tìm thấy mã vạch: ${code}`;
                        }
                    })
                    .catch(error => {
                        console.error("Lỗi khi tìm sản phẩm:", error);
                        timSanPham.value = "Lỗi kết nối khi tìm sản phẩm.";
                    });
            });

        }
    </script>
    <script>
        const timSanPham = document.getElementById("timSanPham");
        const ketQuaBang = document.getElementById("ketQuaTimSanPham");
        const idChiTietSpInput = document.getElementById("idChiTietSp");

        timSanPham.addEventListener("input", function () {
            const keyword = this.value.trim();

            const url = keyword.length < 2
                ? `/admin/ban-hang/tim-kiem-san-pham`
                : `/admin/ban-hang/tim-kiem-san-pham?keyword=${encodeURIComponent(keyword)}`;

            fetch(url)
                .then(res => res.json())
                .then(dsSp => {
                    // Lọc bỏ sản phẩm có số lượng = 0
                    const spHienThi = dsSp.filter(sp => sp.soLuong > 0);

                    if (!spHienThi || spHienThi.length === 0) {
                        ketQuaBang.innerHTML = `
                        <tr>
                            <td colspan="4" class="text-center text-muted">Không tìm thấy sản phẩm nào</td>
                        </tr>`;
                        return;
                    }

                    ketQuaBang.innerHTML = spHienThi.map((sp, index) => `
                    <tr onclick="chonSanPham('${sp.id}', '${sp.tenSanPham} - ${sp.mauSac} / ${sp.kichThuoc} - SL: ${sp.soLuong}')">
                        <td class="p-3">${index + 1}</td>
                        <td class="p-3">${sp.ma || ''}</td>
                        <td class="p-3">
                            <img src="${sp.hinhAnh}" alt="Ảnh" class="w-12 h-12 object-cover rounded">
                        </td>
                        <td class="p-3">${sp.tenSanPham} - ${sp.mauSac} / ${sp.kichThuoc} - SL: ${sp.soLuong}</td>
                        <td class="p-3">
                            <button class="btn btn-primary btn-them-gio"
                                    onclick="themVaoGioHang('${sp.id}')">
                                Thêm vào giỏ
                            </button>
                        </td>
                    </tr>
                `).join('');
                })
                .catch(err => {
                    console.error(err);
                    ketQuaBang.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center text-danger">Lỗi khi tìm kiếm sản phẩm</td>
                    </tr>`;
                });
        });

        function chonSanPham(id, text) {
            document.getElementById("timSanPham").value = text;
            idChiTietSpInput.value = id;
        }

        function themVaoGioHang(idChiTietSp) {
            const csrfToken = document.querySelector('input[name="_csrf"]')?.value;

            // Lấy cartKey từ thẻ input hidden (ví dụ)
            const cartKey = document.querySelector('input[name="cartKey"]')?.value;

            if (!cartKey) {
                alert("❌ Thiếu cartKey!");
                return;
            }

            const payload = {
                idChiTietSp: idChiTietSp,
                soLuong: 1,
                cartKey: cartKey
            };

            fetch('/admin/ban-hang/them-gio-hang', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...(csrfToken && { 'X-CSRF-TOKEN': csrfToken })
                },
                body: JSON.stringify(payload)
            })
                .then(res => {
                    if (res.ok) return res.text();
                    return res.text().then(text => { throw new Error(text); });
                })
                .then(data => {
                    alert("✅ Đã thêm vào giỏ hàng");
                    window.location.reload();
                    // Có thể cập nhật giao diện giỏ hàng tại đây
                })
                .catch(err => {
                    console.error("Lỗi khi thêm sản phẩm:", err);
                    alert("❌ Không thể thêm sản phẩm vào giỏ: " + err.message);
                });
        }

        function validateSoLuong(form) {
            const input = form.querySelector('input[name="soLuong"]');
            const soLuongMoi = parseInt(input.value);
            const soLuongTon = parseInt(input.dataset.ton);

            if (soLuongMoi > soLuongTon) {
                alert("❌ Số lượng vượt quá tồn kho: " + soLuongTon);
                return false;
            }
            return true;
        }
        function toggleQrImage() {
            const ckRadio = document.getElementById('pttt_ck');
            const qrDiv = document.getElementById('qrChuyenKhoan');
            if (ckRadio.checked) {
                qrDiv.style.display = 'block';
            } else {
                qrDiv.style.display = 'none';
            }
        }

        document.addEventListener("DOMContentLoaded", function () {
            toggleQrImage();
        });
        document.addEventListener("DOMContentLoaded", function () {
            const checkbox = document.getElementById("muonVanChuyen");
            const diaChiDiv = document.getElementById("diaChiVanChuyen");

            checkbox.addEventListener("change", function () {
                if (this.checked) {
                    diaChiDiv.style.display = "block";
                    calculateShipping();
                } else {
                    diaChiDiv.style.display = "none";
                }
            });

            document.getElementById("btnTaoDonVanChuyen").addEventListener("click", function () {
                // TODO: Gọi API tạo đơn vận chuyển tại đây (GHN hoặc GHTK)
                alert("Đã gửi yêu cầu tạo đơn vận chuyển!");
            });
        });
    </script>


</section>
</body>
</html>