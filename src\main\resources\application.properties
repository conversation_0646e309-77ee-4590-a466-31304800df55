spring.application.name=DATN_SD31
logging.pattern.console=${LOGPATTERN_CONSOLE:%green(%d{HH:mm:ss.SSS}) %yellow(%-5level) %red([%thread]) %blue(%logger{255}) - %msg%n}

spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.cache=false

#DB Connect
spring.datasource.url=**********************************************************************************************;
spring.datasource.username=sa
spring.datasource.password=123
spring.datasource.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# File upload configuration
#file.upload-dir=src/main/resources/static/uploads/

logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.show-sql=false
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

vnpay.tmnCode=W0QGUAGG
vnpay.hashSecret=6QFTFA6YY4C76REKGAMPSYRCAW87AINL
vnpay.payUrl=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
vnpay.returnUrl=http://localhost:8080/vnpay-payment-return

# File upload configuration
file.upload-dir=src/main/resources/static/uploads/