<!DOCTYPE html>
<!--C<PERSON><PERSON> hình layout cho Thymleaf-->
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{admin/layout}">
<head>
    <title>Voucher</title>
    <!--    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet"-->
    <!--          integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT" crossorigin="anonymous">-->

    <!--    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">-->
<!--    <style>-->
<!--        .filter-section {-->
<!--            background-color: #f8f9fa;-->
<!--            padding: 20px;-->
<!--            border-radius: 8px;-->
<!--            margin-bottom: 30px;-->
<!--        }-->

<!--        .filter-buttons .btn {-->
<!--            margin-right: 10px;-->
<!--            margin-bottom: 10px;-->
<!--        }-->

<!--        .table-section {-->
<!--            background-color: #fff;-->
<!--            padding: 20px;-->
<!--            border-radius: 8px;-->
<!--            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);-->
<!--        }-->

<!--        .table-section .table thead th {-->
<!--            background-color: #f1f1f1;-->
<!--        }-->

<!--        .action-btn {-->
<!--            border: none;-->
<!--            background: none;-->
<!--            cursor: pointer;-->
<!--            font-size: 1.2rem;-->
<!--            padding: 5px;-->
<!--        }-->

<!--        .action-btn.accept {-->
<!--            color: green;-->
<!--        }-->

<!--        .action-btn.cancel {-->
<!--            color: red;-->
<!--        }-->
<!--    </style>-->
    <style>
        /*CSS Thong bao*/
        .custom-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 40px 12px 18px; /* hoặc: padding: 12px 18px; */
            border-radius: 6px;
            color: white;
            z-index: 9999;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
            min-width: 280px;
            animation: fadeIn 0.3s ease-in-out;
            overflow: hidden;
        }


        .custom-toast.success {
            background-color: #28a745;
        }

        .custom-toast.error {
            background-color: #dc3545;
        }

        .custom-toast .close-btn {
            position: absolute;
            top: 6px;
            right: 8px;
            background: transparent;
            border: none;
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            line-height: 1;
            cursor: pointer;
            padding: 0;
        }

        .custom-toast .progress-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 4px;
            background-color: rgba(255, 255, 255, 0.7);
            animation: progressBar 4s linear forwards;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes progressBar {
            from {
                width: 100%;
            }
            to {
                width: 0%;
            }
        }

        /*CSS Thong bao*/
    </style>
</head>
<body>

<section layout:fragment="content">

    <!-- Error Toast -->
    <div id="customError" class="custom-toast error" style="display:none;">
        <i class="bi bi-x-circle-fill me-2"></i>
        <span id="errorMsg"></span>
        <button class="close-btn" onclick="closeToast('customError')">&times;</button>
        <div class="progress-bar"></div>
    </div>

    <div class="container mt-4">
        <h2 class="mb-4 text-center">Chỉnh sửa Phiếu giảm giá</h2>
    </div>

    <div class="container">
        <form
                th:action="@{/admin/phieu-giam-gia/update}"
                th:object="${phieuGiamGia}"
                method="post"
        >
            <input type="hidden" th:field="*{id}" />
            <div th:if="${#fields.hasGlobalErrors()}">
                <div
                        class="alert alert-danger"
                        th:each="err : ${#fields.globalErrors()}"
                        th:text="${err}"
                ></div>
            </div>

            <div class="mb-3">
                <label>Tên</label>
                <input type="text" class="form-control" th:field="*{ten}" maxlength="100" />
                <div class="text-danger" th:errors="*{ten}"></div>
            </div>

            <div class="mb-3">
                <label>Mã</label>
                <input type="text" class="form-control" th:field="*{ma}" maxlength="50"/>
                <div class="text-danger" th:errors="*{ma}"></div>
            </div>

            <div class="mb-3">
                <label>Loại Phiếu</label>
                <select
                        class="form-select"
                        th:field="*{loaiPhieuGiamGia}"
                        id="loaiPhieuGiamGia"
                >
                    <option value="">-- Chọn loại phiếu --</option>
                    <option value="1">Giảm %</option>
                    <option value="2">Giảm tiền</option>
                </select>
                <div class="text-danger" th:errors="*{loaiPhieuGiamGia}"></div>
            </div>

            <div class="mb-3">
                <label>Ngày bắt đầu</label>
                <input type="date" class="form-control" th:field="*{ngayBatDau}" />
                <div class="text-danger" th:errors="*{ngayBatDau}"></div>
            </div>

            <div class="mb-3">
                <label>Ngày kết thúc</label>
                <input type="date" class="form-control" th:field="*{ngayKetThuc}" />
                <div class="text-danger" th:errors="*{ngayKetThuc}"></div>
            </div>

            <div class="mb-3">
                <label>Mức giảm</label>
                <input type="number" class="form-control" th:field="*{mucDo}" />
                <div class="text-danger" th:errors="*{mucDo}"></div>
            </div>

            <div class="mb-3" id="divGiamToiDa">
                <label>Giảm tối đa</label>
                <input type="number" class="form-control" th:field="*{giamToiDa}" />
                <div class="text-danger" th:errors="*{giamToiDa}"></div>
            </div>

            <div class="mb-3">
                <label>Điều kiện</label>
                <input type="number" class="form-control" th:field="*{dieuKien}" />
                <div class="text-danger" th:errors="*{dieuKien}"></div>
            </div>

            <div class="mb-3">
                <label>Số lượng</label>
                <input type="number" class="form-control" th:field="*{soLuongTon}" />
                <div class="text-danger" th:errors="*{soLuongTon}"></div>
            </div>

            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">Lưu</button>
                <a href="/admin/phieu-giam-gia" class="btn btn-secondary">Hủy</a>
            </div>
        </form>
    </div>
</section>

<!--  Thong bao-->
<script th:inline="javascript">
    /*<![CDATA[*/
    const successMsg = [[${success} == null ? 'null' : '' + ${success} + '']];
    const errorMsg = [[${error} == null ? 'null' : '' + ${error} + '']];

    function showCustomToast(type, message) {
        const toastId = type === 'success' ? 'customSuccess' : 'customError';
        const toastEl = document.getElementById(toastId);
        const msgEl = document.getElementById(type === 'success' ? 'successMsg' : 'errorMsg');
        const progressBar = toastEl.querySelector('.progress-bar');

        msgEl.textContent = message;
        toastEl.style.display = 'block';

        // Khởi động lại animation nếu toast được hiển thị lại
        progressBar.style.animation = 'none';
        void progressBar.offsetWidth; // force reflow
        progressBar.style.animation = null;

        // Auto close sau 4s
        setTimeout(() => {
            toastEl.style.display = 'none';
        }, 4000);
    }

    function closeToast(toastId) {
        const toastEl = document.getElementById(toastId);
        toastEl.style.display = 'none';
    }

    document.addEventListener("DOMContentLoaded", function () {
        if (successMsg && successMsg !== "null") showCustomToast('success', successMsg);
        if (errorMsg && errorMsg !== "null") showCustomToast('error', errorMsg);
    });
    /*]]>*/
</script>
<!--  Thong bao-->

<script>
    $(document).ready(function () {
        $("#loaiPhieuGiamGia").on("change", function () {
            const selectedValue = parseInt($(this).val());
            if (selectedValue === 1) {
                $("#mucDo").attr("placeholder", "Nhập phần trăm giảm giá");
                $("#divGiamToiDa").show();
                $("#giamToiDa").attr("required", true).val(0);
            } else if (selectedValue === 2) {
                $("#mucDo").attr("placeholder", "Nhập số tiền giảm");
                $("#divGiamToiDa").hide();
                $("#giamToiDa").removeAttr("required").val(0);
            }
        });
    });
</script>

</body>
</html>